//
//  OnboardingManager.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI
import Combine

@MainActor
class OnboardingManager: ObservableObject {
    @Published var currentState: OnboardingState = .splash
    @Published var hasCompletedOnboarding: Bool = false
    @Published var isFirstLaunch: Bool = true
    
    private let userDefaults = UserDefaults.standard
    
    init() {
        loadOnboardingState()
    }
    
    // MARK: - Public Methods
    func startOnboarding() {
        if isFirstLaunch {
            currentState = .splash
        } else if hasCompletedOnboarding {
            currentState = .welcome
        } else {
            currentState = .intro
        }
    }
    
    func completeSplash() {
        if isFirstLaunch && !hasCompletedOnboarding {
            currentState = .intro
        } else {
            currentState = .welcome
        }
    }
    
    func completeIntro() {
        currentState = .welcome
        markOnboardingCompleted()
    }
    
    func showWelcome() {
        currentState = .welcome
    }
    
    func skipOnboarding() {
        markOnboardingCompleted()
        currentState = .welcome
    }
    
    // MARK: - Private Methods
    private func loadOnboardingState() {
        hasCompletedOnboarding = userDefaults.bool(forKey: AppConstants.StorageKeys.onboardingCompleted)
        isFirstLaunch = !userDefaults.bool(forKey: "has_launched_before")
        
        // Mark that app has been launched
        if isFirstLaunch {
            userDefaults.set(true, forKey: "has_launched_before")
        }
    }
    
    private func markOnboardingCompleted() {
        hasCompletedOnboarding = true
        userDefaults.set(true, forKey: AppConstants.StorageKeys.onboardingCompleted)
    }
    
    // MARK: - Reset (for testing)
    func resetOnboarding() {
        hasCompletedOnboarding = false
        isFirstLaunch = true
        userDefaults.removeObject(forKey: AppConstants.StorageKeys.onboardingCompleted)
        userDefaults.removeObject(forKey: "has_launched_before")
        currentState = .splash
    }
}

// MARK: - Onboarding State
enum OnboardingState {
    case splash
    case intro
    case welcome
}
