//
//  OnboardingData.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

// MARK: - Onboarding Page Model
struct OnboardingPage: Identifiable, Hashable {
    let id = UUID()
    let title: String
    let subtitle: String
    let description: String
    let systemImage: String
    let primaryColor: Color
    let secondaryColor: Color
    let backgroundGradient: [Color]
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: OnboardingPage, rhs: OnboardingPage) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Onboarding Data
struct OnboardingData {
    static let pages: [OnboardingPage] = [
        OnboardingPage(
            title: "Learn Anywhere",
            subtitle: "Study at Your Own Pace",
            description: "Access your courses, assignments, and materials from anywhere. Learn on your schedule with our flexible platform.",
            systemImage: "book.fill",
            primaryColor: AppConstants.Colors.primary,
            secondaryColor: AppConstants.Colors.primaryLight,
            backgroundGradient: [
                Color(red: 0.9, green: 0.95, blue: 1.0),
                Color(red: 0.8, green: 0.9, blue: 1.0)
            ]
        ),
        
        OnboardingPage(
            title: "Track Progress",
            subtitle: "Monitor Your Achievement",
            description: "Keep track of your learning progress, grades, and achievements. Stay motivated with detailed insights and analytics.",
            systemImage: "chart.line.uptrend.xyaxis",
            primaryColor: AppConstants.Colors.success,
            secondaryColor: Color(red: 0.6, green: 0.9, blue: 0.7),
            backgroundGradient: [
                Color(red: 0.9, green: 1.0, blue: 0.95),
                Color(red: 0.8, green: 0.95, blue: 0.9)
            ]
        ),
        
        OnboardingPage(
            title: "Connect & Collaborate",
            subtitle: "Join Your Learning Community",
            description: "Connect with classmates, participate in discussions, and collaborate on projects. Learning is better together.",
            systemImage: "person.3.fill",
            primaryColor: AppConstants.Colors.secondary,
            secondaryColor: Color(red: 1.0, green: 0.8, blue: 0.5),
            backgroundGradient: [
                Color(red: 1.0, green: 0.97, blue: 0.9),
                Color(red: 1.0, green: 0.93, blue: 0.8)
            ]
        )
    ]
}
