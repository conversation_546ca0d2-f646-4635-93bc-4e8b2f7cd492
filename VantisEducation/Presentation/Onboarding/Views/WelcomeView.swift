//
//  WelcomeView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct WelcomeView: View {
    @State private var isAnimating = false
    @State private var logoScale: CGFloat = 0.8
    @State private var textOpacity: Double = 0.0
    @State private var buttonsOpacity: Double = 0.0
    @State private var showLogin = false
    @State private var showRegister = false
    
    var body: some View {
        ZStack {
            // Modern gradient background
            modernGradientBackground
                .ignoresSafeArea()
            
            // Main content
            VStack(spacing: 0) {
                // Top section with branding
                topBrandingSection
                    .frame(maxHeight: .infinity)
                
                // Bottom section with actions
                bottomActionSection
                    .padding(.bottom, 50)
            }
        }
        .onAppear {
            startAnimation()
        }
        .sheet(isPresented: $showLogin) {
            LoginView()
        }
        .sheet(isPresented: $showRegister) {
            RegisterView()
        }
    }
    
    // MARK: - Modern Gradient Background
    private var modernGradientBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.2, green: 0.1, blue: 0.3),
                    Color(red: 0.3, green: 0.2, blue: 0.4),
                    Color(red: 0.1, green: 0.2, blue: 0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Animated overlay elements
            ForEach(0..<4, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 0,
                            endRadius: 150
                        )
                    )
                    .frame(width: 300, height: 300)
                    .offset(
                        x: CGFloat(index * 80 - 120),
                        y: CGFloat(index * 100 - 150)
                    )
                    .scaleEffect(isAnimating ? 1.2 : 0.8)
                    .animation(
                        .easeInOut(duration: 3.0)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.5),
                        value: isAnimating
                    )
            }
        }
    }
    
    // MARK: - Top Branding Section
    private var topBrandingSection: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Logo and app icon
            VStack(spacing: 24) {
                // App icon with background
                ZStack {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 120, height: 120)
                        .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
                        .scaleEffect(logoScale)
                    
                    Image(systemName: "graduationcap.fill")
                        .font(.system(size: 50, weight: .bold))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [
                                    AppConstants.Colors.primary,
                                    AppConstants.Colors.primaryDeep
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(logoScale)
                }
                
                // App name and tagline
                VStack(spacing: 12) {
                    Text(AppConstants.appName)
                        .font(.beVietnamPro(.bold, size: 48))
                        .foregroundColor(.white)
                        .tracking(2)
                        .opacity(textOpacity)
                    
                    Text(AppConstants.appTagline)
                        .font(AppConstants.Typography.title3)
                        .foregroundColor(.white.opacity(0.8))
                        .tracking(1)
                        .opacity(textOpacity)
                }
            }
            
            // Welcome message
            VStack(spacing: 16) {
                Text("Everything you need is in one place")
                    .font(.beVietnamPro(.semiBold, size: 24))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .opacity(textOpacity)
                
                Text("Find your daily necessities at Brand. The world's largest fashion e-commerce has arrived in a mobile shop now")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, 32)
                    .opacity(textOpacity)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Bottom Action Section
    private var bottomActionSection: some View {
        VStack(spacing: 20) {
            // Login button
            Button(action: {
                showLogin = true
            }) {
                Text("Login")
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        AppConstants.Colors.primary,
                                        AppConstants.Colors.primaryDeep
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(
                                color: AppConstants.Colors.primary.opacity(0.3),
                                radius: 15,
                                x: 0,
                                y: 8
                            )
                    )
            }
            .opacity(buttonsOpacity)
            
            // Register button
            Button(action: {
                showRegister = true
            }) {
                Text("Register")
                    .font(.beVietnamPro(.medium, size: 18))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.1))
                            )
                    )
            }
            .opacity(buttonsOpacity)
        }
        .padding(.horizontal, 32)
    }
    
    // MARK: - Animation
    private func startAnimation() {
        // Logo animation
        withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2)) {
            logoScale = 1.0
        }
        
        // Text animation
        withAnimation(.easeInOut(duration: 0.8).delay(0.5)) {
            textOpacity = 1.0
        }
        
        // Buttons animation
        withAnimation(.easeInOut(duration: 0.6).delay(0.8)) {
            buttonsOpacity = 1.0
        }
        
        // Start background animations
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isAnimating = true
        }
    }
}

// MARK: - Preview
#Preview {
    WelcomeView()
}
