//
//  OnboardingView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct OnboardingView: View {
    @State private var currentPage = 0
    @State private var dragOffset: CGFloat = 0
    let onComplete: () -> Void
    
    private let pages = OnboardingData.pages
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Page content
                TabView(selection: $currentPage) {
                    ForEach(Array(pages.enumerated()), id: \.element.id) { index, page in
                        OnboardingPageView(page: page)
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.5), value: currentPage)
                
                // Custom controls overlay
                VStack {
                    Spacer()
                    
                    // Bottom controls
                    bottomControls
                        .padding(.horizontal, 32)
                        .padding(.bottom, 50)
                }
            }
        }
        .ignoresSafeArea()
    }
    
    // MARK: - Bottom Controls
    private var bottomControls: some View {
        VStack(spacing: 32) {
            // Page indicator
            pageIndicator
            
            // Action buttons
            actionButtons
        }
    }
    
    // MARK: - Page Indicator
    private var pageIndicator: some View {
        HStack(spacing: 12) {
            ForEach(0..<pages.count, id: \.self) { index in
                Capsule()
                    .fill(index == currentPage ? pages[currentPage].primaryColor : Color.gray.opacity(0.3))
                    .frame(
                        width: index == currentPage ? 24 : 8,
                        height: 8
                    )
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentPage)
            }
        }
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        HStack(spacing: 16) {
            // Skip button (only show if not on last page)
            if currentPage < pages.count - 1 {
                Button("Skip") {
                    onComplete()
                }
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(maxWidth: .infinity)
            }
            
            // Next/Get Started button
            Button(action: nextAction) {
                HStack(spacing: 8) {
                    Text(currentPage == pages.count - 1 ? "Get Started" : "Next")
                        .font(.beVietnamPro(.semiBold, size: 18))
                        .foregroundColor(.white)
                    
                    if currentPage < pages.count - 1 {
                        Image(systemName: "arrow.right")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    pages[currentPage].primaryColor,
                                    pages[currentPage].secondaryColor
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: pages[currentPage].primaryColor.opacity(0.3),
                            radius: 15,
                            x: 0,
                            y: 8
                        )
                )
            }
            .animation(.easeInOut(duration: 0.3), value: currentPage)
        }
    }
    
    // MARK: - Actions
    private func nextAction() {
        if currentPage < pages.count - 1 {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                currentPage += 1
            }
        } else {
            onComplete()
        }
    }
}

// MARK: - Preview
#Preview {
    OnboardingView {
        print("Onboarding completed")
    }
}
