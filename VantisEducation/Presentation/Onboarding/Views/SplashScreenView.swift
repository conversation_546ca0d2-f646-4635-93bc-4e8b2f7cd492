//
//  SplashScreenView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct SplashScreenView: View {
    @State private var isAnimating = false
    @State private var logoScale: CGFloat = 0.5
    @State private var logoOpacity: Double = 0.0
    @State private var textOpacity: Double = 0.0
    @State private var backgroundOpacity: Double = 0.0
    @State private var showPulse = false
    @State private var rotationAngle: Double = 0
    
    let onComplete: () -> Void
    
    var body: some View {
        ZStack {
            // Animated gradient background
            animatedBackground
                .ignoresSafeArea()
            
            // Main content
            VStack(spacing: 30) {
                Spacer()
                
                // Logo section
                logoSection
                
                // App name and tagline
                textSection
                
                Spacer()
                
                // Loading indicator
                loadingSection
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            startAnimation()
        }
    }
    
    // MARK: - Animated Background
    private var animatedBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.2, green: 0.1, blue: 0.3),
                    Color(red: 0.3, green: 0.2, blue: 0.4),
                    Color(red: 0.1, green: 0.2, blue: 0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .opacity(backgroundOpacity)
            
            // Animated overlay circles
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 0,
                            endRadius: 200
                        )
                    )
                    .frame(width: 400, height: 400)
                    .offset(
                        x: CGFloat(index * 100 - 100),
                        y: CGFloat(index * 150 - 200)
                    )
                    .scaleEffect(isAnimating ? 1.2 : 0.8)
                    .animation(
                        .easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.5),
                        value: isAnimating
                    )
            }
        }
    }
    
    // MARK: - Logo Section
    private var logoSection: some View {
        ZStack {
            // Pulse effect background
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            AppConstants.Colors.primary.opacity(0.3),
                            AppConstants.Colors.primary.opacity(0.1),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 0,
                        endRadius: 80
                    )
                )
                .frame(width: 160, height: 160)
                .scaleEffect(showPulse ? 1.2 : 1.0)
                .opacity(showPulse ? 0.0 : 1.0)
                .animation(
                    .easeInOut(duration: 1.5)
                    .repeatForever(autoreverses: false),
                    value: showPulse
                )
            
            // Logo background circle
            Circle()
                .fill(Color.white)
                .frame(width: 120, height: 120)
                .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
                .scaleEffect(logoScale)
                .opacity(logoOpacity)
            
            // Logo icon
            Image(systemName: "graduationcap.fill")
                .font(.system(size: 50, weight: .bold))
                .foregroundStyle(
                    LinearGradient(
                        colors: [
                            AppConstants.Colors.primary,
                            AppConstants.Colors.primaryDeep
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .scaleEffect(logoScale)
                .opacity(logoOpacity)
                .rotationEffect(.degrees(rotationAngle))
        }
    }
    
    // MARK: - Text Section
    private var textSection: some View {
        VStack(spacing: 12) {
            Text(AppConstants.appName)
                .font(.beVietnamPro(.bold, size: 42))
                .foregroundColor(.white)
                .tracking(2)
                .opacity(textOpacity)
            
            Text(AppConstants.appTagline)
                .font(AppConstants.Typography.title3)
                .foregroundColor(.white.opacity(0.8))
                .tracking(1)
                .opacity(textOpacity)
        }
    }
    
    // MARK: - Loading Section
    private var loadingSection: some View {
        VStack(spacing: 16) {
            // Custom loading dots
            HStack(spacing: 8) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(Color.white.opacity(0.8))
                        .frame(width: 8, height: 8)
                        .scaleEffect(isAnimating ? 1.2 : 0.8)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                            value: isAnimating
                        )
                }
            }
            .opacity(textOpacity)
            
            Text("Loading...")
                .font(AppConstants.Typography.caption)
                .foregroundColor(.white.opacity(0.6))
                .opacity(textOpacity)
        }
    }
    
    // MARK: - Animation Functions
    private func startAnimation() {
        // Background fade in
        withAnimation(.easeIn(duration: 0.5)) {
            backgroundOpacity = 1.0
        }
        
        // Logo animation sequence
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6)) {
                logoScale = 1.0
                logoOpacity = 1.0
            }
            
            // Start logo rotation
            withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }
        }
        
        // Text fade in
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeInOut(duration: 0.6)) {
                textOpacity = 1.0
            }
        }
        
        // Start other animations
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isAnimating = true
            showPulse = true
        }
        
        // Complete splash screen
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            withAnimation(.easeOut(duration: 0.5)) {
                logoOpacity = 0.0
                textOpacity = 0.0
                backgroundOpacity = 0.0
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                onComplete()
            }
        }
    }
}

// MARK: - Preview
#Preview {
    SplashScreenView {
        print("Splash screen completed")
    }
}
