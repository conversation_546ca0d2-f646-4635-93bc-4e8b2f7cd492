//
//  OnboardingPageView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct OnboardingPageView: View {
    let page: OnboardingPage
    @State private var isAnimating = false
    @State private var imageScale: CGFloat = 0.8
    @State private var textOpacity: Double = 0.0

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: page.backgroundGradient,
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                // Decorative background elements
                decorativeBackground

                // Main content
                VStack(spacing: 0) {
                    // Top section with illustration
                    illustrationSection
                        .frame(height: geometry.size.height * 0.5)

                    // Bottom section with text content
                    contentSection
                        .frame(height: geometry.size.height * 0.5)
                }
            }
        }
        .onAppear {
            startAnimation()
        }
    }
    
    // MARK: - Decorative Background
    private var decorativeBackground: some View {
        ZStack {
            // Floating circles
            ForEach(0..<5, id: \.self) { index in
                Circle()
                    .fill(page.primaryColor.opacity(0.1))
                    .frame(
                        width: CGFloat.random(in: 50...120),
                        height: CGFloat.random(in: 50...120)
                    )
                    .offset(
                        x: CGFloat.random(in: -200...200),
                        y: CGFloat.random(in: -300...300)
                    )
                    .scaleEffect(isAnimating ? 1.1 : 0.9)
                    .animation(
                        .easeInOut(duration: Double.random(in: 2...4))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.3),
                        value: isAnimating
                    )
            }
        }
    }
    
    // MARK: - Illustration Section
    private var illustrationSection: some View {
        VStack {
            Spacer()
            
            ZStack {
                // Background circle for icon
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                page.primaryColor.opacity(0.2),
                                page.secondaryColor.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 200, height: 200)
                    .scaleEffect(imageScale)
                    .shadow(color: page.primaryColor.opacity(0.3), radius: 20, x: 0, y: 10)
                
                // Main icon
                Image(systemName: page.systemImage)
                    .font(.system(size: 80, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [page.primaryColor, page.secondaryColor],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(imageScale)
                
                // Animated rings around icon
                ForEach(0..<2, id: \.self) { index in
                    Circle()
                        .stroke(
                            page.primaryColor.opacity(0.3),
                            lineWidth: 2
                        )
                        .frame(width: 220 + CGFloat(index * 40), height: 220 + CGFloat(index * 40))
                        .scaleEffect(isAnimating ? 1.1 : 1.0)
                        .opacity(isAnimating ? 0.0 : 0.5)
                        .animation(
                            .easeInOut(duration: 2.0)
                            .repeatForever(autoreverses: false)
                            .delay(Double(index) * 0.5),
                            value: isAnimating
                        )
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                // Title
                Text(page.title)
                    .font(.beVietnamPro(.bold, size: 32))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .opacity(textOpacity)
                
                // Subtitle
                Text(page.subtitle)
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(page.primaryColor)
                    .multilineTextAlignment(.center)
                    .opacity(textOpacity)
            }
            
            // Description
            Text(page.description)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
                .padding(.horizontal, 32)
                .opacity(textOpacity)
            
            Spacer()
        }
        .padding(.top, 40)
    }
    
    // MARK: - Animation
    private func startAnimation() {
        // Image animation
        withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2)) {
            imageScale = 1.0
        }
        
        // Text animation
        withAnimation(.easeInOut(duration: 0.8).delay(0.5)) {
            textOpacity = 1.0
        }
        
        // Start continuous animations
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            isAnimating = true
        }
    }
}

// MARK: - Preview
#Preview {
    OnboardingPageView(page: OnboardingData.pages[0])
}
