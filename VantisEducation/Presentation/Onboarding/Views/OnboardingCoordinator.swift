//
//  OnboardingCoordinator.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct OnboardingCoordinator: View {
    @StateObject private var onboardingManager = OnboardingManager()
    let onComplete: () -> Void
    
    var body: some View {
        Group {
            switch onboardingManager.currentState {
            case .splash:
                SplashScreenView {
                    onboardingManager.completeSplash()
                }
                
            case .intro:
                OnboardingView {
                    onboardingManager.completeIntro()
                }
                
            case .welcome:
                WelcomeView()
                    .onAppear {
                        // Complete onboarding flow after showing welcome
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            onComplete()
                        }
                    }
            }
        }
        .onAppear {
            onboardingManager.startOnboarding()
        }
        .animation(.easeInOut(duration: 0.5), value: onboardingManager.currentState)
    }
}

// MARK: - Preview
#Preview {
    OnboardingCoordinator {
        print("Onboarding completed")
    }
}
