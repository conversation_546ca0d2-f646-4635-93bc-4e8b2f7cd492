//
//  SocialLoginButton.swift
//  VantisEducation
//
//  Created by Augment Agent on 31/7/25.
//

import SwiftUI

enum SocialLoginProvider {
    case apple
    case google
    case facebook
    
    var title: String {
        switch self {
        case .apple:
            return "Continue with Apple"
        case .google:
            return "Continue with Google"
        case .facebook:
            return "Continue with Facebook"
        }
    }
    
    var iconName: String {
        switch self {
        case .apple:
            return "apple.logo"
        case .google:
            return "globe" // We'll use a system icon for now, can be replaced with custom Google icon
        case .facebook:
            return "f.circle.fill"
        }
    }
    
    var backgroundColor: Color {
        switch self {
        case .apple:
            return .black
        case .google:
            return .white
        case .facebook:
            return Color(red: 0.26, green: 0.40, blue: 0.70) // Facebook blue
        }
    }
    
    var foregroundColor: Color {
        switch self {
        case .apple:
            return .white
        case .google:
            return .black
        case .facebook:
            return .white
        }
    }
    
    var borderColor: Color {
        switch self {
        case .apple:
            return .clear
        case .google:
            return Color(.systemGray4)
        case .facebook:
            return .clear
        }
    }
}

struct SocialLoginButton: View {
    let provider: SocialLoginProvider
    let action: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            // Add haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            action()
        }) {
            HStack(spacing: 12) {
                Image(systemName: provider.iconName)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(provider.foregroundColor)
                
                Text(provider.title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(provider.foregroundColor)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(provider.backgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(provider.borderColor, lineWidth: provider == .google ? 1 : 0)
                    )
                    .shadow(
                        color: Color.black.opacity(0.1),
                        radius: isPressed ? 2 : 8,
                        x: 0,
                        y: isPressed ? 1 : 4
                    )
            )
        }
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Preview
struct SocialLoginButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            SocialLoginButton(provider: .apple) {
                print("Apple login tapped")
            }
            
            SocialLoginButton(provider: .google) {
                print("Google login tapped")
            }
            
            SocialLoginButton(provider: .facebook) {
                print("Facebook login tapped")
            }
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
