//
//  ModernTextField.swift
//  VantisEducation
//
//  Created by Augment Agent on 31/7/25.
//

import SwiftUI

struct ModernTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let isSecure: Bool
    let keyboardType: UIKeyboardType
    let textContentType: UITextContentType?
    let icon: String
    let errorMessage: String?
    let submitLabel: SubmitLabel

    @FocusState private var isFocused: Bool
    @State private var isSecureVisible = false

    init(
        title: String,
        placeholder: String,
        text: Binding<String>,
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        textContentType: UITextContentType? = nil,
        icon: String,
        errorMessage: String? = nil,
        submitLabel: SubmitLabel = .done
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.textContentType = textContentType
        self.icon = icon
        self.errorMessage = errorMessage
        self.submitLabel = submitLabel
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Title
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color(.label))
                .opacity(0.8)
            
            // Input Field
            HStack(spacing: 12) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isFocused ? .accentColor : Color(.systemGray))
                    .frame(width: 20)
                
                // Text Field
                Group {
                    if isSecure && !isSecureVisible {
                        SecureField(placeholder, text: $text)
                            .submitLabel(submitLabel)
                    } else {
                        TextField(placeholder, text: $text)
                            .submitLabel(submitLabel)
                    }
                }
                .font(.system(size: 16))
                .textContentType(textContentType)
                .keyboardType(keyboardType)
                .autocapitalization(.none)
                .autocorrectionDisabled()
                .focused($isFocused)
                
                // Password visibility toggle
                if isSecure {
                    Button(action: {
                        isSecureVisible.toggle()
                        // Add haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                    }) {
                        Image(systemName: isSecureVisible ? "eye.slash" : "eye")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color(.systemGray))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                errorMessage != nil ? Color.red :
                                isFocused ? Color.accentColor : Color.clear,
                                lineWidth: errorMessage != nil ? 1.5 : (isFocused ? 2 : 0)
                            )
                    )
            )
            .animation(.easeInOut(duration: 0.2), value: isFocused)
            
            // Error Message
            if let errorMessage = errorMessage {
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.circle.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.red)
                    
                    Text(errorMessage)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.red)
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: errorMessage)
    }
}

// MARK: - Preview
struct ModernTextField_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ModernTextField(
                title: "Email",
                placeholder: "Enter your email",
                text: .constant(""),
                keyboardType: .emailAddress,
                textContentType: .emailAddress,
                icon: "envelope"
            )
            
            ModernTextField(
                title: "Password",
                placeholder: "Enter your password",
                text: .constant(""),
                isSecure: true,
                textContentType: .password,
                icon: "lock"
            )
            
            ModernTextField(
                title: "Email",
                placeholder: "Enter your email",
                text: .constant("invalid@"),
                keyboardType: .emailAddress,
                textContentType: .emailAddress,
                icon: "envelope",
                errorMessage: "Please enter a valid email address"
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
