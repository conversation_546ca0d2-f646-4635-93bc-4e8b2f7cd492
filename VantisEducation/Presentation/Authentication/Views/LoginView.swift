//
//  LoginView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct LoginView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var animateElements = false
    @State private var showRegister = false
    @State private var showForgotPassword = false
    @FocusState private var focusedField: LoginField?
    
    enum LoginField: Hashable {
        case email, password
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Modern gradient background
                modernGradientBackground
                    .ignoresSafeArea(.all)

                ScrollView(.vertical, showsIndicators: false) {
                    VStack(spacing: 0) {
                        // Top section with logo and branding
                        topBrandingSection
                            .frame(minHeight: geometry.size.height * 0.4)

                        // Bottom section with form
                        bottomFormSection
                            .padding(.bottom, 50) // Extra padding for scroll
                    }
                    .frame(minHeight: geometry.size.height)
                }
                .scrollIndicators(.hidden)
            }
        }
        .navigationBarHidden(true)
        .ignoresSafeArea(.all)
        .onAppear {
            animateElements = true
        }
        .alert("Error", isPresented: $authViewModel.showError) {
            Button("OK") {
                authViewModel.hideError()
            }
        } message: {
            Text(authViewModel.authError ?? "An error occurred")
        }
        .sheet(isPresented: $showRegister) {
            RegisterView()
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
    }
    
    // MARK: - Modern Gradient Background
    private var modernGradientBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.2, green: 0.1, blue: 0.3),
                    Color(red: 0.3, green: 0.2, blue: 0.4),
                    Color(red: 0.1, green: 0.2, blue: 0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Animated overlay elements
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 0,
                            endRadius: 200
                        )
                    )
                    .frame(width: 400, height: 400)
                    .offset(
                        x: CGFloat(index * 100 - 100),
                        y: CGFloat(index * 150 - 200)
                    )
                    .scaleEffect(animateElements ? 1.2 : 0.8)
                    .animation(
                        .easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.5),
                        value: animateElements
                    )
            }
        }
    }
    
    // MARK: - Top Branding Section
    private var topBrandingSection: some View {
        VStack(spacing: 20) {
            Spacer()
            
            // Back button (if needed)
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "arrow.left")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.2))
                        )
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            // Logo and app name
            VStack(spacing: 16) {
                // Logo icon
                Image(systemName: "graduationcap.fill")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [
                                AppConstants.Colors.primary,
                                AppConstants.Colors.primaryDeep
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                Text(AppConstants.appName)
                    .font(.beVietnamPro(.bold, size: 28))
                    .foregroundColor(.white)
                    .tracking(1)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Bottom Form Section
    private var bottomFormSection: some View {
        VStack(spacing: 0) {
            VStack(spacing: 32) {
                // Header
                VStack(spacing: 8) {
                    Text("Login")
                        .font(.beVietnamPro(.bold, size: 32))
                        .foregroundColor(.primary)

                    Text("Login to continue using the app")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)

                // Form fields
                modernFormSection

                // Action buttons
                modernActionSection

                // Social login
                socialLoginSection

                // Register section
                modernRegisterSection
            }
            .padding(.bottom, 40) // Add bottom padding
            .background(
                RoundedRectangle(cornerRadius: 40)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 30, x: 0, y: -10)
            )
            .padding(.horizontal, 20)
        }
    }

    // MARK: - Modern Form Section
    private var modernFormSection: some View {
        VStack(spacing: 20) {
            // Email field
            ModernTextField(
                title: "Email",
                placeholder: "Enter your email",
                text: $authViewModel.loginEmail,
                isSecure: false,
                keyboardType: .emailAddress,
                textContentType: .emailAddress,
                icon: "envelope.fill",
                submitLabel: .next
            )
            .focused($focusedField, equals: .email)

            // Password field
            ModernTextField(
                title: "Password",
                placeholder: "Enter password",
                text: $authViewModel.loginPassword,
                isSecure: true,
                keyboardType: .default,
                textContentType: .password,
                icon: "lock.fill",
                submitLabel: .done
            )
            .focused($focusedField, equals: .password)

            // Forgot password
            HStack {
                Spacer()
                Button("Forgot Password?") {
                    showForgotPassword = true
                }
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.primary)
            }
        }
        .onSubmit {
            switch focusedField {
            case .email:
                focusedField = .password
            case .password:
                // Attempt login when password field submits
                if authViewModel.isLoginFormValid {
                    Task {
                        await authViewModel.login()
                    }
                }
                focusedField = nil
            case .none:
                break
            }
        }
        .padding(.horizontal, 32)
    }

    // MARK: - Modern Action Section
    private var modernActionSection: some View {
        VStack(spacing: 20) {
            // Login button
            Button(action: {
                Task {
                    await authViewModel.login()
                }
            }) {
                HStack(spacing: 12) {
                    if authViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.9)
                    }

                    Text(authViewModel.isLoading ? "Signing In..." : "Login")
                        .font(.beVietnamPro(.semiBold, size: 18))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    AppConstants.Colors.primary,
                                    AppConstants.Colors.primaryDeep
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 15, x: 0, y: 8)
                )
                .scaleEffect(authViewModel.isLoading ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: authViewModel.isLoading)
            }
            .disabled(!authViewModel.isLoginFormValid || authViewModel.isLoading)
            .opacity(authViewModel.isLoginFormValid ? 1.0 : 0.6)
        }
        .padding(.horizontal, 32)
    }

    // MARK: - Social Login Section
    private var socialLoginSection: some View {
        VStack(spacing: 20) {
            // Divider
            HStack {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 1)

                Text("Or Login with")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding(.horizontal, 16)

                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 1)
            }

            // Social buttons
            HStack(spacing: 16) {
                // Facebook
                Button(action: {}) {
                    Image(systemName: "f.square.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.blue)
                        .frame(width: 56, height: 56)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                }

                // Google
                Button(action: {}) {
                    Image(systemName: "globe")
                        .font(.system(size: 24))
                        .foregroundColor(.red)
                        .frame(width: 56, height: 56)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                }

                // Apple
                Button(action: {}) {
                    Image(systemName: "apple.logo")
                        .font(.system(size: 24))
                        .foregroundColor(.black)
                        .frame(width: 56, height: 56)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                }
            }
        }
        .padding(.horizontal, 32)
    }

    // MARK: - Modern Register Section
    private var modernRegisterSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Don't have an account?")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Button("Register") {
                    showRegister = true
                }
                .font(.beVietnamPro(.semiBold, size: 16))
                .foregroundColor(AppConstants.Colors.primary)
            }
        }
        .padding(.bottom, 32)
    }
}

// MARK: - Preview
#Preview {
    LoginView()
        .environmentObject(AuthViewModel())
}
