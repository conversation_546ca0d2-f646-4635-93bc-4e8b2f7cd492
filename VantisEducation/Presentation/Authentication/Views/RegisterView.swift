//
//  RegisterView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct RegisterView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authViewModel = AuthViewModel()
    @State private var animateElements = false
    @FocusState private var focusedField: RegisterField?
    
    // Form fields
    @State private var username = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var acceptTerms = false
    
    enum RegisterField: Hashable {
        case username, email, password, confirmPassword
    }
    
    var body: some View {
        ZStack {
            // Modern gradient background
            modernGradientBackground
                .ignoresSafeArea(.all)
                .allowsHitTesting(false)
            
            ScrollView {
                VStack(spacing: 0) {
                    // Top section with logo and branding
                    topBrandingSection
                        .frame(minHeight: 200)
                    
                    // Bottom section with form
                    bottomFormSection
                }
            }
            .scrollIndicators(.hidden)
            .allowsHitTesting(true)
        }
        .navigationBarHidden(true)
        .ignoresSafeArea(.all)
        .onAppear {
            animateElements = true
        }
        .alert("Error", isPresented: $authViewModel.showError) {
            Button("OK") {
                authViewModel.hideError()
            }
        } message: {
            Text(authViewModel.authError ?? "An error occurred")
        }
    }
    
    // MARK: - Modern Gradient Background
    private var modernGradientBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.2, green: 0.1, blue: 0.3),
                    Color(red: 0.3, green: 0.2, blue: 0.4),
                    Color(red: 0.1, green: 0.2, blue: 0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Animated overlay elements
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 0,
                            endRadius: 200
                        )
                    )
                    .frame(width: 400, height: 400)
                    .offset(
                        x: CGFloat(index * 100 - 100),
                        y: CGFloat(index * 150 - 200)
                    )
                    .scaleEffect(animateElements ? 1.2 : 0.8)
                    .animation(
                        .easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.5),
                        value: animateElements
                    )
            }
        }
    }
    
    // MARK: - Top Branding Section
    private var topBrandingSection: some View {
        VStack(spacing: 20) {
            Spacer()
            
            // Back button
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "arrow.left")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.2))
                        )
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            // Logo and app name
            VStack(spacing: 16) {
                // Logo icon
                Image(systemName: "graduationcap.fill")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [
                                AppConstants.Colors.primary,
                                AppConstants.Colors.primaryDeep
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                Text(AppConstants.appName)
                    .font(.beVietnamPro(.bold, size: 28))
                    .foregroundColor(.white)
                    .tracking(1)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Bottom Form Section
    private var bottomFormSection: some View {
        VStack(spacing: 0) {
            VStack(spacing: 32) {
                // Header
                VStack(spacing: 8) {
                    Text("Register")
                        .font(.beVietnamPro(.bold, size: 32))
                        .foregroundColor(.primary)

                    Text("Enter Your Personal Information")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)

                // Form fields
                modernFormSection

                // Action buttons
                modernActionSection

                // Login section
                modernLoginSection
            }
            .background(
                RoundedRectangle(cornerRadius: 40)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 30, x: 0, y: -10)
            )
            .padding(.horizontal, 20)
        }
    }

    // MARK: - Modern Form Section
    private var modernFormSection: some View {
        VStack(spacing: 20) {
            // Username field
            ModernTextField(
                title: "Username",
                placeholder: "Enter your name",
                text: $username,
                isSecure: false,
                keyboardType: .default,
                textContentType: .name,
                icon: "person.fill",
                submitLabel: .next
            )
            .focused($focusedField, equals: .username)

            // Email field
            ModernTextField(
                title: "Email",
                placeholder: "Enter your email",
                text: $email,
                isSecure: false,
                keyboardType: .emailAddress,
                textContentType: .emailAddress,
                icon: "envelope.fill",
                submitLabel: .next
            )
            .focused($focusedField, equals: .email)

            // Password field
            ModernTextField(
                title: "Password",
                placeholder: "Enter password",
                text: $password,
                isSecure: true,
                keyboardType: .default,
                textContentType: .newPassword,
                icon: "lock.fill",
                submitLabel: .next
            )
            .focused($focusedField, equals: .password)

            // Confirm Password field
            ModernTextField(
                title: "Confirm password",
                placeholder: "Enter confirm password",
                text: $confirmPassword,
                isSecure: true,
                keyboardType: .default,
                textContentType: .newPassword,
                icon: "lock.fill",
                submitLabel: .done
            )
            .focused($focusedField, equals: .confirmPassword)
        }
        .onSubmit {
            switch focusedField {
            case .username:
                focusedField = .email
            case .email:
                focusedField = .password
            case .password:
                focusedField = .confirmPassword
            case .confirmPassword:
                // Attempt register when last field submits
                if isFormValid {
                    Task {
                        await registerUser()
                    }
                }
                focusedField = nil
            case .none:
                break
            }
        }
        .padding(.horizontal, 32)
    }

    // MARK: - Modern Action Section
    private var modernActionSection: some View {
        VStack(spacing: 20) {
            // Register button
            Button(action: {
                Task {
                    await registerUser()
                }
            }) {
                HStack(spacing: 12) {
                    if authViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.9)
                    }

                    Text(authViewModel.isLoading ? "Creating Account..." : "Register")
                        .font(.beVietnamPro(.semiBold, size: 18))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    AppConstants.Colors.primary,
                                    AppConstants.Colors.primaryDeep
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 15, x: 0, y: 8)
                )
                .scaleEffect(authViewModel.isLoading ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: authViewModel.isLoading)
            }
            .disabled(!isFormValid || authViewModel.isLoading)
            .opacity(isFormValid ? 1.0 : 0.6)
        }
        .padding(.horizontal, 32)
    }

    // MARK: - Modern Login Section
    private var modernLoginSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Don't have an account?")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Button("Register") {
                    dismiss()
                }
                .font(.beVietnamPro(.semiBold, size: 16))
                .foregroundColor(AppConstants.Colors.primary)
            }
        }
        .padding(.bottom, 32)
    }

    // MARK: - Computed Properties
    private var isFormValid: Bool {
        return !username.isEmpty &&
               email.isValidEmail &&
               password.isValidPassword &&
               confirmPassword == password
    }

    // MARK: - Actions
    private func registerUser() async {
        // For now, just show a message that registration is not available
        authViewModel.showErrorMessage("Registration is not available. Please contact administrator.")
    }
}
