//
//  ModernLoginView.swift
//  VantisEducation
//
//  Created by Augment Agent on 31/7/25.
//

import SwiftUI

struct ModernLoginView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var email = ""
    @State private var password = ""
    @State private var rememberMe = false
    @State private var showForgotPassword = false
    @State private var keyboardHeight: CGFloat = 0
    
    // Form validation
    @State private var emailError: String?
    @State private var passwordError: String?
    
    // Animation states
    @State private var animateContent = false
    @State private var animateLogo = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                backgroundView
                
                // Content
                ScrollView {
                    VStack(spacing: 0) {
                        // Logo and Header Section
                        headerSection
                            .frame(minHeight: max(200, geometry.size.height * 0.35))
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : -30)
                        
                        // Login Form Section
                        loginFormSection
                            .padding(.bottom, keyboardHeight > 0 ? 20 : 40)
                            .opacity(animateContent ? 1 : 0)
                            .offset(y: animateContent ? 0 : 50)
                    }
                }
                .scrollIndicators(.hidden)
                .scrollDismissesKeyboard(.interactively)
            }
        }
        .ignoresSafeArea(.keyboard, edges: .bottom)
        .navigationBarHidden(true)
        .onAppear {
            setupKeyboardObservers()
            startAnimations()
        }
        .onDisappear {
            removeKeyboardObservers()
        }
        .onTapGesture {
            hideKeyboard()
        }
        .sheet(isPresented: $showForgotPassword) {
            ModernForgotPasswordView()
        }
        .alert("Login Error", isPresented: $authViewModel.showError) {
            Button("OK") {
                authViewModel.hideError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "An error occurred")
        }
    }
    
    // MARK: - Background View
    private var backgroundView: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.95, green: 0.97, blue: 1.0),  // Very light blue
                    Color(red: 0.98, green: 0.95, blue: 1.0),  // Very light purple
                    Color.white
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Subtle pattern overlay
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.blue.opacity(0.1),
                            Color.clear
                        ],
                        center: .topTrailing,
                        startRadius: 0,
                        endRadius: 300
                    )
                )
                .frame(width: 400, height: 400)
                .offset(x: 150, y: -200)
                .scaleEffect(animateLogo ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: animateLogo)
            
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.purple.opacity(0.08),
                            Color.clear
                        ],
                        center: .bottomLeading,
                        startRadius: 0,
                        endRadius: 250
                    )
                )
                .frame(width: 300, height: 300)
                .offset(x: -100, y: 200)
                .scaleEffect(animateLogo ? 0.9 : 1.0)
                .animation(.easeInOut(duration: 4).repeatForever(autoreverses: true), value: animateLogo)
        }
        .ignoresSafeArea()
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Logo
            VStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .shadow(color: Color.blue.opacity(0.3), radius: 20, x: 0, y: 10)
                    
                    Image(systemName: "graduationcap.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                .scaleEffect(animateLogo ? 1.05 : 1.0)
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: animateLogo)
                
                VStack(spacing: 8) {
                    Text("Vantis Student")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                    
                    Text("Viện Tư vấn và Đào tạo Doanh nghiệp")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            Spacer()
        }
    }

    // MARK: - Login Form Section
    private var loginFormSection: some View {
        VStack(spacing: 0) {
            // Form Container
            VStack(spacing: 24) {
                // Welcome Text
                VStack(spacing: 8) {
                    Text("Welcome Back")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)

                    Text("Sign in to continue your learning journey")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 32)

                // Form Fields
                VStack(spacing: 20) {
                    ModernTextField(
                        title: "Email",
                        placeholder: "Enter your email",
                        text: $email,
                        keyboardType: .emailAddress,
                        textContentType: .emailAddress,
                        icon: "envelope",
                        errorMessage: emailError
                    )
                    .onChange(of: email) { _, newValue in
                        validateEmail(newValue)
                        authViewModel.loginEmail = newValue
                    }

                    ModernTextField(
                        title: "Password",
                        placeholder: "Enter your password",
                        text: $password,
                        isSecure: true,
                        textContentType: .password,
                        icon: "lock",
                        errorMessage: passwordError
                    )
                    .onChange(of: password) { _, newValue in
                        validatePassword(newValue)
                        authViewModel.loginPassword = newValue
                    }
                }

                // Remember Me & Forgot Password
                HStack {
                    Button(action: {
                        rememberMe.toggle()
                        authViewModel.rememberMe = rememberMe
                        // Add haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: rememberMe ? "checkmark.square.fill" : "square")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(rememberMe ? .accentColor : Color(.systemGray))

                            Text("Remember me")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.primary)
                        }
                    }

                    Spacer()

                    Button(action: {
                        showForgotPassword = true
                    }) {
                        Text("Forgot Password?")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.accentColor)
                    }
                }
                .padding(.horizontal, 4)

                // Sign In Button
                Button(action: {
                    hideKeyboard()
                    Task {
                        await signIn()
                    }
                }) {
                    HStack(spacing: 12) {
                        if authViewModel.isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "arrow.right")
                                .font(.system(size: 16, weight: .medium))
                        }

                        Text(authViewModel.isLoading ? "Signing In..." : "Sign In")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: isFormValid ? [Color.blue, Color.blue.opacity(0.8)] : [Color(.systemGray4), Color(.systemGray5)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(
                                color: isFormValid ? Color.blue.opacity(0.3) : Color.clear,
                                radius: 10,
                                x: 0,
                                y: 5
                            )
                    )
                }
                .disabled(!isFormValid || authViewModel.isLoading)
                .scaleEffect(authViewModel.isLoading ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: authViewModel.isLoading)

                // Divider
                HStack {
                    Rectangle()
                        .fill(Color(.systemGray4))
                        .frame(height: 1)

                    Text("or")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 16)

                    Rectangle()
                        .fill(Color(.systemGray4))
                        .frame(height: 1)
                }
                .padding(.vertical, 8)

                // Social Login Buttons
                VStack(spacing: 12) {
                    SocialLoginButton(provider: .apple) {
                        Task {
                            await signInWithApple()
                        }
                    }

                    HStack(spacing: 12) {
                        SocialLoginButton(provider: .google) {
                            Task {
                                await signInWithGoogle()
                            }
                        }

                        SocialLoginButton(provider: .facebook) {
                            Task {
                                await signInWithFacebook()
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .background(
                RoundedRectangle(cornerRadius: 32)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.05), radius: 20, x: 0, y: -5)
            )
            .padding(.horizontal, 20)
        }
    }

    // MARK: - Computed Properties
    private var isFormValid: Bool {
        return !email.isEmpty &&
               !password.isEmpty &&
               emailError == nil &&
               passwordError == nil &&
               email.isValidEmail &&
               password.count >= 6
    }

    // MARK: - Helper Methods
    private func startAnimations() {
        withAnimation(.easeOut(duration: 0.8).delay(0.2)) {
            animateContent = true
        }

        withAnimation(.easeInOut(duration: 1.0).delay(0.5)) {
            animateLogo = true
        }
    }

    private func validateEmail(_ email: String) {
        if email.isEmpty {
            emailError = nil
        } else if !email.isValidEmail {
            emailError = "Please enter a valid email address"
        } else {
            emailError = nil
        }
    }

    private func validatePassword(_ password: String) {
        if password.isEmpty {
            passwordError = nil
        } else if password.count < 6 {
            passwordError = "Password must be at least 6 characters"
        } else {
            passwordError = nil
        }
    }

    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    // MARK: - Authentication Methods
    private func signIn() async {
        guard isFormValid else { return }

        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        await authViewModel.login()
    }

    private func signInWithApple() async {
        print("Sign in with Apple tapped")
        // TODO: Implement Apple Sign In
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    private func signInWithGoogle() async {
        print("Sign in with Google tapped")
        // TODO: Implement Google Sign In
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    private func signInWithFacebook() async {
        print("Sign in with Facebook tapped")
        // TODO: Implement Facebook Sign In
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    // MARK: - Keyboard Handling
    private func setupKeyboardObservers() {
        NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillShowNotification,
            object: nil,
            queue: .main
        ) { notification in
            if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
                withAnimation(.easeInOut(duration: 0.3)) {
                    keyboardHeight = keyboardFrame.height
                }
            }
        }

        NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillHideNotification,
            object: nil,
            queue: .main
        ) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                keyboardHeight = 0
            }
        }
    }

    private func removeKeyboardObservers() {
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
    }
}



// MARK: - Preview
struct ModernLoginView_Previews: PreviewProvider {
    static var previews: some View {
        ModernLoginView()
            .environmentObject(AuthViewModel())
    }
}
