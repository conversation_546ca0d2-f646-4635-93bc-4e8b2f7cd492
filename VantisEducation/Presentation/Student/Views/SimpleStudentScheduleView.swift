//
//  SimpleStudentScheduleView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct SimpleStudentScheduleView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "calendar")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Lịch học")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Xem lịch trình học tập của bạn")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Lịch học")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    SimpleStudentScheduleView()
}
