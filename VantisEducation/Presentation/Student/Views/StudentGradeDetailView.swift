//
//  StudentGradeDetailView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct StudentGradeDetailView: View {
    let grade: StudentGrade
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header with grade
                    VStack(spacing: 16) {
                        // Grade display
                        VStack(spacing: 8) {
                            Text("\(grade.gradeText)/\(grade.maxGradeText)")
                                .font(.beVietnamPro(.bold, size: 48))
                                .foregroundColor(grade.gradeColor)
                            
                            Text(grade.percentageText)
                                .font(.beVietnamPro(.semiBold, size: 18))
                                .foregroundColor(grade.gradeColor)
                            
                            Text(performanceText)
                                .font(.beVietnamPro(.medium, size: 16))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 32)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(grade.gradeColor.opacity(0.05))
                        )
                        
                        // Performance indicator
                        VStack(spacing: 8) {
                            HStack {
                                Text("Thang điểm")
                                    .font(.beVietnamPro(.medium, size: 14))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                
                                Spacer()
                                
                                Text("0")
                                    .font(.beVietnamPro(.medium, size: 12))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                
                                Spacer()
                                
                                Text("10")
                                    .font(.beVietnamPro(.medium, size: 12))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }
                            
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    // Background bar
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.gray.opacity(0.2))
                                        .frame(height: 8)
                                    
                                    // Progress bar
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(grade.gradeColor)
                                        .frame(width: geometry.size.width * (grade.grade / grade.maxGrade), height: 8)
                                }
                            }
                            .frame(height: 8)
                        }
                    }
                    
                    // Assignment info
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Thông tin bài kiểm tra")
                            .font(.beVietnamPro(.semiBold, size: 20))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        VStack(spacing: 12) {
                            InfoRow(title: "Môn học", value: grade.subject)
                            InfoRow(title: "Tên bài", value: grade.assignmentName)
                            InfoRow(title: "Loại", value: grade.type.rawValue)
                            InfoRow(title: "Ngày làm", value: grade.dateFormatted)
                            InfoRow(title: "Học kỳ", value: grade.semester)
                            InfoRow(title: "Trọng số", value: "\(String(format: "%.0f", grade.weight * 100))%")
                        }
                    }
                    
                    // Feedback section
                    if let feedback = grade.feedback, !feedback.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Nhận xét của giáo viên")
                                .font(.beVietnamPro(.semiBold, size: 20))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text(feedback)
                                .font(.beVietnamPro(.medium, size: 16))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .padding(16)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.gray.opacity(0.05))
                                )
                        }
                    }
                    
                    // Grade breakdown (if applicable)
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Phân tích điểm số")
                            .font(.beVietnamPro(.semiBold, size: 20))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        VStack(spacing: 8) {
                            GradeBreakdownRow(
                                title: "Điểm đạt được",
                                value: grade.gradeText,
                                color: grade.gradeColor
                            )
                            
                            GradeBreakdownRow(
                                title: "Điểm tối đa",
                                value: grade.maxGradeText,
                                color: AppConstants.Colors.textSecondary
                            )
                            
                            Divider()
                            
                            GradeBreakdownRow(
                                title: "Tỷ lệ đạt được",
                                value: grade.percentageText,
                                color: grade.gradeColor
                            )
                            
                            GradeBreakdownRow(
                                title: "Xếp loại",
                                value: performanceText,
                                color: grade.gradeColor
                            )
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.05))
                        )
                    }
                    
                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationTitle("Chi tiết điểm số")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        // Share grade
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
        }
    }
    
    private var performanceText: String {
        let percentage = grade.percentage
        if percentage >= 85 {
            return "Xuất sắc"
        } else if percentage >= 70 {
            return "Tốt"
        } else if percentage >= 55 {
            return "Trung bình"
        } else {
            return "Cần cải thiện"
        }
    }
}

// MARK: - Supporting Views

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.beVietnamPro(.medium, size: 15))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Spacer()
            
            Text(value)
                .font(.beVietnamPro(.semiBold, size: 15))
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
        .padding(.vertical, 4)
    }
}

struct GradeBreakdownRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.beVietnamPro(.medium, size: 15))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Spacer()
            
            Text(value)
                .font(.beVietnamPro(.bold, size: 15))
                .foregroundColor(color)
        }
        .padding(.vertical, 2)
    }
}

// MARK: - Preview
#Preview {
    StudentGradeDetailView(grade: StudentGrade.mockGrades[0])
}
