//
//  SimpleStudentGradesView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct SimpleStudentGradesView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "star.fill")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Điểm số")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Xem kết quả học tập của bạn")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Điểm số")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    SimpleStudentGradesView()
}
