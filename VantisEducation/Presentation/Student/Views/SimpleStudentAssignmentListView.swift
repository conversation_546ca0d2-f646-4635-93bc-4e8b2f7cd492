//
//  SimpleStudentAssignmentListView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct SimpleStudentAssignmentListView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Bài tập")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Xem và nộp bài tập của bạn")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Bài tập")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    SimpleStudentAssignmentListView()
}
