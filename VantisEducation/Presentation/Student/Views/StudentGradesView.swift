//
//  StudentGradesView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct StudentGradesView: View {
    @StateObject private var viewModel = StudentGradesViewModel()
    @State private var selectedSubject: String = "Tất cả"
    @State private var selectedSemester: String = "Học kỳ hiện tại"
    @State private var showingGradeDetail: StudentGrade?
    
    var filteredGrades: [StudentGrade] {
        var grades = viewModel.grades
        
        if selectedSubject != "Tất cả" {
            grades = grades.filter { $0.subject == selectedSubject }
        }
        
        return grades
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Header Section
                    headerSection
                    
                    // Overall Stats Section
                    overallStatsSection
                    
                    // Filter Section
                    filterSection
                    
                    // Grades List Section
                    gradesListSection
                    
                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationBarHidden(true)
            .background(Color.white.ignoresSafeArea())
            .refreshable {
                await viewModel.refreshGrades()
            }
            .sheet(item: $showingGradeDetail) { grade in
                StudentGradeDetailView(grade: grade)
            }
        }
        .task {
            await viewModel.loadGrades()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Button(action: {
                    // Navigate back
                }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(AppConstants.Colors.primary)
                }
                
                Spacer()
                
                Text("Điểm số")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button(action: {
                    // Export grades
                }) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.title2)
                        .foregroundColor(AppConstants.Colors.primary)
                }
            }
            
            Text("Theo dõi kết quả học tập của bạn")
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Overall Stats Section
    private var overallStatsSection: some View {
        VStack(spacing: 16) {
            // GPA Card
            VStack(spacing: 8) {
                Text("Điểm trung bình")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(String(format: "%.2f", viewModel.overallGPA))
                    .font(.beVietnamPro(.bold, size: 36))
                    .foregroundColor(gpaColor(viewModel.overallGPA))
                
                Text("/ 10.0")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                AppConstants.Colors.primary.opacity(0.1),
                                AppConstants.Colors.primary.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            
            // Stats Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                GradeStatCard(
                    title: "Tổng môn",
                    value: "\(viewModel.totalSubjects)",
                    icon: "book.fill",
                    color: .blue
                )
                
                GradeStatCard(
                    title: "Điểm cao nhất",
                    value: String(format: "%.1f", viewModel.highestGrade),
                    icon: "star.fill",
                    color: .green
                )
                
                GradeStatCard(
                    title: "Cần cải thiện",
                    value: "\(viewModel.needImprovementCount)",
                    icon: "exclamationmark.triangle.fill",
                    color: .orange
                )
            }
        }
        .padding(.vertical, 16)
    }
    
    // MARK: - Filter Section
    private var filterSection: some View {
        VStack(spacing: 12) {
            // Subject Filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(viewModel.availableSubjects, id: \.self) { subject in
                        GradeFilterChip(
                            title: subject,
                            isSelected: selectedSubject == subject
                        ) {
                            selectedSubject = subject
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            
            // Semester Filter
            HStack {
                Text("Học kỳ:")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
                
                Menu {
                    ForEach(viewModel.availableSemesters, id: \.self) { semester in
                        Button(semester) {
                            selectedSemester = semester
                        }
                    }
                } label: {
                    HStack {
                        Text(selectedSemester)
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Image(systemName: "chevron.down")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Grades List Section
    private var gradesListSection: some View {
        LazyVStack(spacing: 12) {
            if viewModel.isLoading && viewModel.grades.isEmpty {
                ForEach(0..<5, id: \.self) { _ in
                    GradeCardSkeleton()
                }
            } else if filteredGrades.isEmpty {
                EmptyGradesView()
                    .padding(.top, 40)
            } else {
                ForEach(filteredGrades) { grade in
                    StudentGradeCard(grade: grade) {
                        showingGradeDetail = grade
                    }
                }
            }
        }
    }
    
    private func gpaColor(_ gpa: Double) -> Color {
        if gpa >= 8.5 {
            return .green
        } else if gpa >= 7.0 {
            return .blue
        } else if gpa >= 5.5 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Supporting Views

struct GradeStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(.beVietnamPro(.medium, size: 11))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

struct GradeFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.beVietnamPro(.medium, size: 13))
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textSecondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? AppConstants.Colors.primary : Color.gray.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct EmptyGradesView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.bar.doc.horizontal")
                .font(.system(size: 48))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text("Chưa có điểm số")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Điểm số sẽ được cập nhật sau khi giáo viên chấm bài")
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 40)
    }
}

struct GradeCardSkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 80, height: 20)
                
                Spacer()
                
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 40, height: 20)
            }
            
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.3))
                .frame(height: 16)
            
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 100, height: 12)
                
                Spacer()
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 60, height: 12)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(
            Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true),
            value: isAnimating
        )
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Preview
#Preview {
    StudentGradesView()
}
