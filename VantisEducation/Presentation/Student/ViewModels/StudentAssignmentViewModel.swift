//
//  StudentAssignmentViewModel.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import Foundation
import SwiftUI

@MainActor
class StudentAssignmentViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var assignments: [Assignment] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Computed Properties
    var pendingCount: Int {
        assignments.filter { !$0.isSubmitted && !$0.isOverdue }.count
    }
    
    var submittedCount: Int {
        assignments.filter { $0.isSubmitted && !$0.isGraded }.count
    }
    
    var gradedCount: Int {
        assignments.filter { $0.isGraded }.count
    }
    
    var overdueCount: Int {
        assignments.filter { $0.isOverdue }.count
    }
    
    // MARK: - Methods
    func loadAssignments() async {
        guard !isLoading else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            
            // Mock data
            assignments = Assignment.mockStudentAssignments
            
        } catch {
            errorMessage = "Không thể tải danh sách bài tập: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func refreshAssignments() async {
        await loadAssignments()
    }
    
    func submitAssignment(_ assignment: Assignment) async {
        // Implementation for submitting assignment
        print("Submitting assignment: \(assignment.title)")
    }
}

// MARK: - Mock Data Extension
extension Assignment {
    static var mockStudentAssignments: [Assignment] {
        [
            Assignment(
                id: "1",
                courseId: "course_1",
                courseName: "Toán học",
                courseCode: "MATH101",
                classId: "class_1",
                title: "Bài tập Toán học - Chương 4",
                description: "Giải các bài tập về phương trình bậc hai và ứng dụng",
                instructions: "Hoàn thành tất cả bài tập từ 1-15 trong worksheet",
                type: .homework,
                status: .active,
                maxScore: 10.0,
                weight: 15.0,
                difficulty: .intermediate,
                estimatedHours: 3,
                assignedDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                dueDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()) ?? Date(),
                submissionMethod: .online,
                allowLateSubmission: true,
                latePenalty: 10.0,
                maxLateDays: 3,
                rubric: nil,
                attachments: [
                    AssignmentAttachment(
                        id: "att_1",
                        fileName: "worksheet_chapter4.pdf",
                        fileType: "pdf",
                        fileSize: 1024000,
                        url: "https://example.com/worksheet.pdf",
                        description: "Worksheet chương 4",
                        isRequired: true,
                        uploadedAt: Date()
                    )
                ],
                tags: ["math", "homework"],
                totalSubmissions: 0,
                gradedSubmissions: 0,
                averageScore: nil,
                instructorId: "instructor_1",
                instructorName: "Thầy Nguyễn",
                metadata: nil,
                createdAt: Date(),
                updatedAt: nil
            ),
            Assignment(
                id: "2",
                courseId: "course_2",
                courseName: "Vật lý",
                courseCode: "PHYS101",
                classId: "class_1",
                title: "Kiểm tra giữa kỳ - Vật lý",
                description: "Kiểm tra kiến thức về động học và động lực học",
                instructions: "Thời gian làm bài: 90 phút",
                type: .exam,
                status: .completed,
                maxScore: 10.0,
                weight: 30.0,
                difficulty: .intermediate,
                estimatedHours: 2,
                assignedDate: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
                dueDate: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
                submissionMethod: .online,
                allowLateSubmission: false,
                latePenalty: nil,
                maxLateDays: nil,
                rubric: nil,
                attachments: [],
                tags: ["physics", "exam"],
                totalSubmissions: 1,
                gradedSubmissions: 1,
                averageScore: 8.5,
                instructorId: "instructor_2",
                instructorName: "Cô Phương",
                metadata: nil,
                createdAt: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
                updatedAt: Calendar.current.date(byAdding: .day, value: -1, to: Date())
            ),
            Assignment(
                id: "3",
                courseId: "course_3",
                courseName: "Văn học",
                courseCode: "LIT101",
                classId: "class_1",
                title: "Bài luận Văn học",
                description: "Phân tích tác phẩm 'Chí Phèo' của Nam Cao",
                instructions: "Bài luận tối thiểu 1000 từ, phân tích tâm lý nhân vật",
                type: .essay,
                status: .active,
                maxScore: 10.0,
                weight: 20.0,
                difficulty: .advanced,
                estimatedHours: 5,
                assignedDate: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date(),
                dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(),
                submissionMethod: .online,
                allowLateSubmission: true,
                latePenalty: 5.0,
                maxLateDays: 2,
                rubric: nil,
                attachments: [
                    AssignmentAttachment(
                        id: "att_3",
                        fileName: "chi_pheo_analysis_guide.pdf",
                        fileType: "pdf",
                        fileSize: 512000,
                        url: "https://example.com/guide.pdf",
                        description: "Hướng dẫn phân tích",
                        isRequired: false,
                        uploadedAt: Date()
                    )
                ],
                tags: ["literature", "essay"],
                totalSubmissions: 0,
                gradedSubmissions: 0,
                averageScore: nil,
                instructorId: "instructor_3",
                instructorName: "Cô Lan",
                metadata: nil,
                createdAt: Date(),
                updatedAt: nil
            )
        ]
    }
}

// MARK: - Assignment Model Extension for Student
extension Assignment {
    // Student-specific properties
    var isSubmitted: Bool {
        // This would come from submission data in real app
        return status == .completed || averageScore != nil
    }

    var isGraded: Bool {
        return averageScore != nil
    }

    var grade: Double? {
        return averageScore
    }

    var maxGrade: Double {
        return maxScore
    }

    var subject: String {
        return courseName
    }

    var submissionDate: Date? {
        return isSubmitted ? updatedAt : nil
    }

    var attachmentNames: [String] {
        return attachments?.map { $0.fileName } ?? []
    }

    var statusText: String {
        if isGraded {
            return "Đã chấm điểm"
        } else if isSubmitted {
            return "Đã nộp"
        } else if isOverdue {
            return "Quá hạn"
        } else {
            return "Chưa nộp"
        }
    }

    var statusColor: Color {
        if isGraded {
            return .green
        } else if isSubmitted {
            return .blue
        } else if isOverdue {
            return .red
        } else {
            return .orange
        }
    }

    var dueDateFormatted: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: dueDate)
    }
}
