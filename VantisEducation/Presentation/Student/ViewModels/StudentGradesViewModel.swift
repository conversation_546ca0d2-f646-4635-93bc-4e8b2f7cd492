//
//  StudentGradesViewModel.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import Foundation
import SwiftUI

@MainActor
class StudentGradesViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var grades: [StudentGrade] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Computed Properties
    var overallGPA: Double {
        guard !grades.isEmpty else { return 0.0 }
        let totalPoints = grades.reduce(0.0) { $0 + $1.grade }
        return totalPoints / Double(grades.count)
    }
    
    var totalSubjects: Int {
        Set(grades.map { $0.subject }).count
    }
    
    var highestGrade: Double {
        grades.map { $0.grade }.max() ?? 0.0
    }
    
    var needImprovementCount: Int {
        grades.filter { $0.grade < 5.5 }.count
    }
    
    var availableSubjects: [String] {
        var subjects = Array(Set(grades.map { $0.subject })).sorted()
        subjects.insert("Tất cả", at: 0)
        return subjects
    }
    
    var availableSemesters: [String] {
        var semesters = Array(Set(grades.map { $0.semester })).sorted()
        semesters.insert("Học kỳ hiện tại", at: 0)
        return semesters
    }
    
    // MARK: - Methods
    func loadGrades() async {
        guard !isLoading else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            
            // Mock data
            grades = StudentGrade.mockGrades
            
        } catch {
            errorMessage = "Không thể tải điểm số: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func refreshGrades() async {
        await loadGrades()
    }
}

// MARK: - StudentGrade Model
struct StudentGrade: Identifiable {
    let id: String
    let subject: String
    let assignmentName: String
    let grade: Double
    let maxGrade: Double
    let date: Date
    let semester: String
    let type: GradeType
    let feedback: String?
    let weight: Double // Trọng số của bài kiểm tra
    
    enum GradeType: String, CaseIterable {
        case assignment = "Bài tập"
        case quiz = "Kiểm tra"
        case midterm = "Giữa kỳ"
        case final = "Cuối kỳ"
        case project = "Dự án"
    }
    
    var percentage: Double {
        (grade / maxGrade) * 100
    }
    
    var gradeText: String {
        String(format: "%.1f", grade)
    }
    
    var maxGradeText: String {
        String(format: "%.0f", maxGrade)
    }
    
    var percentageText: String {
        String(format: "%.1f%%", percentage)
    }
    
    var gradeColor: Color {
        if percentage >= 85 {
            return .green
        } else if percentage >= 70 {
            return .blue
        } else if percentage >= 55 {
            return .orange
        } else {
            return .red
        }
    }
    
    var dateFormatted: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: date)
    }
}

// MARK: - Mock Data
extension StudentGrade {
    static var mockGrades: [StudentGrade] {
        [
            StudentGrade(
                id: "1",
                subject: "Toán học",
                assignmentName: "Kiểm tra chương 4 - Phương trình bậc hai",
                grade: 8.5,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .quiz,
                feedback: "Bài làm tốt, cần chú ý thêm về cách giải phương trình có tham số",
                weight: 0.3
            ),
            StudentGrade(
                id: "2",
                subject: "Vật lý",
                assignmentName: "Bài tập về động học",
                grade: 7.0,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -10, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .assignment,
                feedback: "Cần làm rõ hơn các bước giải",
                weight: 0.2
            ),
            StudentGrade(
                id: "3",
                subject: "Hóa học",
                assignmentName: "Kiểm tra giữa kỳ",
                grade: 9.0,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -15, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .midterm,
                feedback: "Xuất sắc! Nắm vững kiến thức về phản ứng hóa học",
                weight: 0.4
            ),
            StudentGrade(
                id: "4",
                subject: "Văn học",
                assignmentName: "Bài luận phân tích tác phẩm",
                grade: 8.0,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .assignment,
                feedback: "Phân tích sâu sắc, văn phong tốt",
                weight: 0.25
            ),
            StudentGrade(
                id: "5",
                subject: "Tiếng Anh",
                assignmentName: "Speaking Test - Unit 5",
                grade: 7.5,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .quiz,
                feedback: "Good pronunciation, need to improve fluency",
                weight: 0.3
            ),
            StudentGrade(
                id: "6",
                subject: "Toán học",
                assignmentName: "Bài tập về hàm số",
                grade: 6.5,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -12, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .assignment,
                feedback: "Cần ôn lại kiến thức về đạo hàm",
                weight: 0.2
            ),
            StudentGrade(
                id: "7",
                subject: "Lịch sử",
                assignmentName: "Kiểm tra 15 phút",
                grade: 8.8,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -8, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .quiz,
                feedback: "Nắm vững kiến thức lịch sử Việt Nam",
                weight: 0.15
            ),
            StudentGrade(
                id: "8",
                subject: "Địa lý",
                assignmentName: "Dự án nghiên cứu địa hình",
                grade: 9.2,
                maxGrade: 10.0,
                date: Calendar.current.date(byAdding: .day, value: -20, to: Date()) ?? Date(),
                semester: "Học kỳ 1 - 2024",
                type: .project,
                feedback: "Dự án rất chi tiết và có tính thực tiễn cao",
                weight: 0.35
            )
        ]
    }
}
