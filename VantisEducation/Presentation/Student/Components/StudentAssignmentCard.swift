//
//  StudentAssignmentCard.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct StudentAssignmentCard: View {
    let assignment: Assignment
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with subject and status
                HStack {
                    // Subject badge
                    Text(assignment.subject)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(AppConstants.Colors.primary)
                        )
                    
                    Spacer()
                    
                    // Status badge
                    Text(assignment.statusText)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(assignment.statusColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(assignment.statusColor.opacity(0.1))
                        )
                }
                
                // Title and description
                VStack(alignment: .leading, spacing: 4) {
                    Text(assignment.title)
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(assignment.description)
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
                
                // Due date and time remaining
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "calendar")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(assignment.dueDateFormatted)
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Text(assignment.timeRemaining)
                        .font(.beVietnamPro(.semiBold, size: 12))
                        .foregroundColor(assignment.isOverdue ? .red : AppConstants.Colors.primary)
                }
                
                // Grade section (if graded)
                if assignment.isGraded, let grade = assignment.grade {
                    HStack {
                        Text("Điểm số:")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text("\(String(format: "%.1f", grade))/\(String(format: "%.0f", assignment.maxGrade))")
                            .font(.beVietnamPro(.bold, size: 16))
                            .foregroundColor(gradeColor(for: grade, max: assignment.maxGrade))
                    }
                    .padding(.top, 8)
                    .padding(.top, 8)
                    .overlay(
                        Rectangle()
                            .frame(height: 1)
                            .foregroundColor(Color.gray.opacity(0.2)),
                        alignment: .top
                    )
                }
                
                // Action buttons
                if !assignment.isSubmitted && !assignment.isOverdue {
                    HStack(spacing: 12) {
                        Button(action: {
                            // View details
                            onTap()
                        }) {
                            HStack {
                                Image(systemName: "eye")
                                    .font(.caption)
                                Text("Xem chi tiết")
                                    .font(.beVietnamPro(.medium, size: 12))
                            }
                            .foregroundColor(AppConstants.Colors.primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(AppConstants.Colors.primary, lineWidth: 1)
                            )
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            // Submit assignment
                            print("Submit assignment: \(assignment.title)")
                        }) {
                            HStack {
                                Image(systemName: "paperplane.fill")
                                    .font(.caption)
                                Text("Nộp bài")
                                    .font(.beVietnamPro(.semiBold, size: 12))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(AppConstants.Colors.primary)
                            )
                        }
                    }
                    .padding(.top, 8)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white)
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func gradeColor(for grade: Double, max: Double) -> Color {
        let percentage = grade / max
        if percentage >= 0.8 {
            return .green
        } else if percentage >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Student Assignment Card Skeleton
struct StudentAssignmentCardSkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header skeleton
            HStack {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 60, height: 20)
                
                Spacer()
                
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 50, height: 20)
            }
            
            // Title skeleton
            VStack(alignment: .leading, spacing: 4) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 16)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 200, height: 14)
            }
            
            // Date skeleton
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 120, height: 12)
                
                Spacer()
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 80, height: 12)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(
            Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true),
            value: isAnimating
        )
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        StudentAssignmentCard(
            assignment: Assignment.mockStudentAssignments[0]
        ) {
            print("Assignment tapped")
        }
        
        StudentAssignmentCard(
            assignment: Assignment.mockStudentAssignments[1]
        ) {
            print("Assignment tapped")
        }
        
        StudentAssignmentCardSkeleton()
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
