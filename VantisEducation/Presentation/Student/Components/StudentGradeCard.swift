//
//  StudentGradeCard.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct StudentGradeCard: View {
    let grade: StudentGrade
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with subject and grade type
                HStack {
                    // Subject badge
                    Text(grade.subject)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(AppConstants.Colors.primary)
                        )
                    
                    // Grade type badge
                    Text(grade.type.rawValue)
                        .font(.beVietnamPro(.medium, size: 11))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.gray.opacity(0.1))
                        )
                    
                    Spacer()
                    
                    // Grade display
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("\(grade.gradeText)/\(grade.maxGradeText)")
                            .font(.beVietnamPro(.bold, size: 18))
                            .foregroundColor(grade.gradeColor)
                        
                        Text(grade.percentageText)
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(grade.gradeColor)
                    }
                }
                
                // Assignment name
                Text(grade.assignmentName)
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                // Date and weight info
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "calendar")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(grade.dateFormatted)
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: "percent")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text("Trọng số: \(String(format: "%.0f", grade.weight * 100))%")
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                // Feedback preview (if available)
                if let feedback = grade.feedback, !feedback.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Nhận xét:")
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(feedback)
                            .font(.beVietnamPro(.medium, size: 13))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                    .padding(.top, 4)
                    .padding(.top, 4)
                    .overlay(
                        Rectangle()
                            .frame(height: 1)
                            .foregroundColor(Color.gray.opacity(0.2)),
                        alignment: .top
                    )
                }
                
                // Grade performance indicator
                HStack {
                    // Performance bar
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // Background bar
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 4)
                            
                            // Progress bar
                            RoundedRectangle(cornerRadius: 2)
                                .fill(grade.gradeColor)
                                .frame(width: geometry.size.width * (grade.percentage / 100), height: 4)
                        }
                    }
                    .frame(height: 4)
                    
                    // Performance text
                    Text(performanceText)
                        .font(.beVietnamPro(.medium, size: 11))
                        .foregroundColor(grade.gradeColor)
                        .frame(width: 60, alignment: .trailing)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white)
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var performanceText: String {
        let percentage = grade.percentage
        if percentage >= 85 {
            return "Xuất sắc"
        } else if percentage >= 70 {
            return "Tốt"
        } else if percentage >= 55 {
            return "Trung bình"
        } else {
            return "Cần cải thiện"
        }
    }
}

// MARK: - Grade Performance Badge
struct GradePerformanceBadge: View {
    let grade: StudentGrade
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: performanceIcon)
                .font(.caption)
                .foregroundColor(grade.gradeColor)
            
            Text(performanceText)
                .font(.beVietnamPro(.medium, size: 11))
                .foregroundColor(grade.gradeColor)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(grade.gradeColor.opacity(0.1))
        )
    }
    
    private var performanceText: String {
        let percentage = grade.percentage
        if percentage >= 85 {
            return "Xuất sắc"
        } else if percentage >= 70 {
            return "Tốt"
        } else if percentage >= 55 {
            return "Trung bình"
        } else {
            return "Cần cải thiện"
        }
    }
    
    private var performanceIcon: String {
        let percentage = grade.percentage
        if percentage >= 85 {
            return "star.fill"
        } else if percentage >= 70 {
            return "checkmark.circle.fill"
        } else if percentage >= 55 {
            return "minus.circle.fill"
        } else {
            return "exclamationmark.triangle.fill"
        }
    }
}

// MARK: - Compact Grade Card (for dashboard)
struct CompactGradeCard: View {
    let grade: StudentGrade
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Subject icon
                VStack {
                    Image(systemName: subjectIcon)
                        .font(.title3)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Text(grade.subject)
                        .font(.beVietnamPro(.medium, size: 10))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(1)
                }
                .frame(width: 50)
                
                // Grade info
                VStack(alignment: .leading, spacing: 4) {
                    Text(grade.assignmentName)
                        .font(.beVietnamPro(.semiBold, size: 14))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(1)
                    
                    Text(grade.type.rawValue)
                        .font(.beVietnamPro(.medium, size: 11))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                // Grade display
                VStack(alignment: .trailing, spacing: 2) {
                    Text(grade.gradeText)
                        .font(.beVietnamPro(.bold, size: 16))
                        .foregroundColor(grade.gradeColor)
                    
                    Text("/\(grade.maxGradeText)")
                        .font(.beVietnamPro(.medium, size: 11))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white)
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var subjectIcon: String {
        switch grade.subject {
        case "Toán học":
            return "function"
        case "Vật lý":
            return "atom"
        case "Hóa học":
            return "flask"
        case "Văn học":
            return "book"
        case "Tiếng Anh":
            return "globe"
        case "Lịch sử":
            return "clock"
        case "Địa lý":
            return "globe.asia.australia"
        default:
            return "book"
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        StudentGradeCard(
            grade: StudentGrade.mockGrades[0]
        ) {
            print("Grade tapped")
        }
        
        StudentGradeCard(
            grade: StudentGrade.mockGrades[2]
        ) {
            print("Grade tapped")
        }
        
        CompactGradeCard(
            grade: StudentGrade.mockGrades[1]
        ) {
            print("Compact grade tapped")
        }
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
