//
//  APIClient.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import Combine

// MARK: - Network Error
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case serverError(Int, String?)
    case unauthorized
    case networkUnavailable
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noData:
            return "No data received"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        case .serverError(let code, let message):
            return "Server error (\(code)): \(message ?? "Unknown error")"
        case .unauthorized:
            return "Unauthorized access"
        case .networkUnavailable:
            return "Network unavailable"
        case .unknown(let error):
            return "Unknown error: \(error.localizedDescription)"
        }
    }
}

// MARK: - API Response
struct APIResponse<T: Codable>: Codable {
    let data: T?
    let message: String?
    let success: Bool
}

struct EmptyResponse: Codable {
    // Empty response for endpoints that don't return data
}

// MARK: - API Client
class APIClient: ObservableObject {
    static let shared = APIClient()

    private let baseURL: String
    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder
    private let logger = Logger.shared
    private let apiLogger = APILogger.shared

    @Published var isLoading = false
    @Published var networkStatus: NetworkStatus = .connected

    private init() {
        self.baseURL = APIConfiguration.shared.baseURL

        // Configure URLSession with timeout and caching
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = APIConfiguration.shared.timeout
        config.timeoutIntervalForResource = APIConfiguration.shared.timeout * 2
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        config.urlCache = URLCache(memoryCapacity: 10 * 1024 * 1024, diskCapacity: 50 * 1024 * 1024)

        self.session = URLSession(configuration: config)

        // Configure JSON decoder/encoder
        self.decoder = JSONDecoder()
        self.encoder = JSONEncoder()

        // Configure date formatting - use custom strategy to handle multiple formats
        decoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // Try multiple date formats
            let formatters = [
                // ISO8601 with milliseconds and Z
                "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                // ISO8601 with milliseconds and timezone
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
                // ISO8601 without milliseconds
                "yyyy-MM-dd'T'HH:mm:ss'Z'",
                "yyyy-MM-dd'T'HH:mm:ssZ"
            ]

            for format in formatters {
                let formatter = DateFormatter()
                formatter.dateFormat = format
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                formatter.locale = Locale(identifier: "en_US_POSIX")

                if let date = formatter.date(from: dateString) {
                    return date
                }
            }

            throw DecodingError.dataCorrupted(
                DecodingError.Context(
                    codingPath: decoder.codingPath,
                    debugDescription: "Cannot decode date string \(dateString)"
                )
            )
        }

        // Use standard ISO8601 for encoding
        let encoderFormatter = DateFormatter()
        encoderFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        encoderFormatter.timeZone = TimeZone(abbreviation: "UTC")
        encoderFormatter.locale = Locale(identifier: "en_US_POSIX")
        encoder.dateEncodingStrategy = .formatted(encoderFormatter)

        // Start network monitoring
        startNetworkMonitoring()
    }
    
    // MARK: - Network Status
    enum NetworkStatus {
        case connected
        case disconnected
        case poor
    }

    // MARK: - Network Monitoring
    private func startNetworkMonitoring() {
        // TODO: Implement network reachability monitoring
        // This would typically use Network framework
    }

    // MARK: - Generic Request Method
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        parameters: [String: Any]? = nil,
        body: Codable? = nil,
        headers: [String: String]? = nil,
        responseType: T.Type,
        requiresAuth: Bool = true
    ) async throws -> T {

        let startTime = CFAbsoluteTimeGetCurrent()

        guard let url = APIConfiguration.shared.buildURL(endpoint: endpoint) else {
            logger.error("Invalid URL for endpoint: \(endpoint)", category: .network)
            throw NetworkError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue

        // Add default headers from APIConfiguration
        APIConfiguration.shared.defaultHeaders.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }

        // Add authorization header if required and token exists
        if requiresAuth {
            let token = await MainActor.run { TokenManager.shared.getToken() }
            if let token = token {
                request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
        }
        
        // Add custom headers
        headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Enhanced debug logging for all requests
        let requestTimestamp = Date()
        let timestamp = ISO8601DateFormatter().string(from: requestTimestamp)
        NSLog("🌐 ===== API REQUEST =====")
        NSLog("🌐 Timestamp: %@", timestamp)
        NSLog("🌐 URL: %@", url.absoluteString)
        NSLog("🌐 Method: %@", method.rawValue)
        NSLog("🌐 Requires Auth: %@", requiresAuth ? "YES" : "NO")
        NSLog("🌐 Headers:")
        for (key, value) in (request.allHTTPHeaderFields ?? [:]) {
            // Mask sensitive headers for security
            if key.lowercased() == "authorization" {
                let maskedValue = value.count > 20 ? "\(String(value.prefix(20)))..." : value
                NSLog("🌐   %@: %@", key, maskedValue)
            } else {
                NSLog("🌐   %@: %@", key, value)
            }
        }

        // Log request to file
        apiLogger.logRequest(
            url: url,
            method: method.rawValue,
            headers: request.allHTTPHeaderFields,
            body: request.httpBody,
            timestamp: requestTimestamp
        )

        // Add body for POST/PUT requests
        if method != .GET {
            if let body = body {
                do {
                    request.httpBody = try encoder.encode(body)

                    if let bodyData = request.httpBody,
                       let bodyString = String(data: bodyData, encoding: .utf8) {
                        print("🌐 Request Body: \(bodyString)")
                    }
                } catch {
                    throw NetworkError.decodingError(error)
                }
            } else if let parameters = parameters {
                do {
                    request.httpBody = try JSONSerialization.data(withJSONObject: parameters)

                    if let bodyData = request.httpBody,
                       let bodyString = String(data: bodyData, encoding: .utf8) {
                        print("🌐 Request Body: \(bodyString)")
                    }
                } catch {
                    throw NetworkError.decodingError(error)
                }
            }
        }
        print("🌐 ========================")
        
        do {
            let (data, response) = try await session.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.unknown(URLError(.badServerResponse))
            }

            // Debug logging for response
            NSLog("🌐 ===== API RESPONSE =====")
            NSLog("🌐 Status Code: %d", httpResponse.statusCode)
            NSLog("🌐 Response Headers:")
            for (key, value) in httpResponse.allHeaderFields {
                NSLog("🌐   %@: %@", "\(key)", "\(value)")
            }
            NSLog("🌐 Response Body:")
            if let responseString = String(data: data, encoding: .utf8) {
                NSLog("🌐 %@", responseString)
            } else {
                NSLog("🌐 [Unable to decode response as UTF-8 string]")
            }
            NSLog("🌐 ===========================")

            // Calculate request duration
            let duration = CFAbsoluteTimeGetCurrent() - startTime

            // Log response to file
            apiLogger.logResponse(
                url: url,
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields,
                body: data,
                error: nil as Error?,
                duration: duration,
                timestamp: Date()
            )

            // Handle HTTP status codes
            switch httpResponse.statusCode {
            case 200...299:
                break
            case 401:
                // Token expired, clear it
                await MainActor.run { TokenManager.shared.clearToken() }
                throw NetworkError.unauthorized
            case 400...499:
                let errorMessage = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
                let message = errorMessage?["message"] as? String
                throw NetworkError.serverError(httpResponse.statusCode, message)
            case 500...599:
                throw NetworkError.serverError(httpResponse.statusCode, "Server error")
            default:
                throw NetworkError.serverError(httpResponse.statusCode, "Unknown error")
            }
            
            // Decode response
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                return try decoder.decode(T.self, from: data)
            } catch {
                throw NetworkError.decodingError(error)
            }
            
        } catch {
            // Calculate request duration for error case
            let duration = CFAbsoluteTimeGetCurrent() - startTime

            // Log error response to file
            apiLogger.logResponse(
                url: url,
                statusCode: 0, // No status code for network errors
                headers: nil as [AnyHashable: Any]?,
                body: nil as Data?,
                error: error,
                duration: duration,
                timestamp: Date()
            )

            // Also log error details
            apiLogger.logError(error: error, context: "API Request to \(url.absoluteString)")

            if error is NetworkError {
                throw error
            } else {
                throw NetworkError.unknown(error)
            }
        }
    }

    // MARK: - Helper Methods
    private func performRequestWithRetry(request: URLRequest, retryCount: Int = 0) async throws -> (Data, URLResponse) {
        do {
            return try await session.data(for: request)
        } catch {
            // Retry logic for network errors
            if retryCount < APIConfiguration.shared.retryAttempts {
                if shouldRetry(error: error) {
                    logger.warning("Request failed, retrying (\(retryCount + 1)/\(APIConfiguration.shared.retryAttempts))", category: .network)
                    try await Task.sleep(nanoseconds: UInt64(APIConfiguration.shared.retryDelay * 1_000_000_000))
                    return try await performRequestWithRetry(request: request, retryCount: retryCount + 1)
                }
            }
            throw error
        }
    }

    private func shouldRetry(error: Error) -> Bool {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut, .networkConnectionLost, .notConnectedToInternet:
                return true
            default:
                return false
            }
        }
        return false
    }

    private func handleHTTPStatusCode(_ statusCode: Int, data: Data) throws {
        switch statusCode {
        case 200...299:
            break
        case 401:
            // Token expired, clear it and notify auth state
            Task { @MainActor in
                TokenManager.shared.clearToken()
            }
            throw NetworkError.unauthorized
        case 403:
            throw NetworkError.unauthorized
        case 404:
            throw NetworkError.decodingError(NSError(domain: "APIClient", code: -1))
        case 429:
            throw NetworkError.serverError(429, "Too many requests")
        case 400...499:
            let errorMessage = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            let message = errorMessage?["message"] as? String ?? "Client error"
            throw NetworkError.serverError(statusCode, message)
        case 500...599:
            throw NetworkError.serverError(statusCode, "Server error")
        default:
            throw NetworkError.serverError(statusCode, "Unknown error")
        }
    }
}

// MARK: - AnyEncodable Helper
struct AnyEncodable: Encodable {
    private let encodable: Encodable

    init(_ encodable: Encodable) {
        self.encodable = encodable
    }

    func encode(to encoder: Encoder) throws {
        try encodable.encode(to: encoder)
    }
}

// MARK: - HTTP Method
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - Enhanced Token Manager
class TokenManager: ObservableObject {
    @MainActor static let shared = TokenManager()

    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentToken: String?
    @Published var tokenExpiry: Date?

    // MARK: - Private Properties
    private let tokenKey = "linkx_access_token"
    private let userKey = "linkx_current_user"
    private let expiryKey = "linkx_token_expiry"
    private let emailKey = "linkx_user_email"

    private init() {
        loadStoredToken()
    }

    // MARK: - Token Management
    func saveToken(_ token: String, expiresIn: TimeInterval? = nil) {
        print("🔐 TokenManager: Saving token...")

        // Save to Keychain for security
        do {
            try KeychainManager.shared.saveAccessToken(token)
            print("✅ Token saved to Keychain")
        } catch {
            // Fallback to UserDefaults if Keychain fails
            UserDefaults.standard.set(token, forKey: tokenKey)
            print("📱 Token saved to UserDefaults (fallback)")
        }

        // Calculate expiry date
        let expiry = Date().addingTimeInterval(expiresIn ?? 31536000) // Default 1 year (like JWT)
        UserDefaults.standard.set(expiry, forKey: expiryKey)

        // Update state
        currentToken = token
        tokenExpiry = expiry
        isAuthenticated = true

        print("✅ Token saved successfully. Expires: \(expiry)")
    }

    func loadStoredToken() {
        print("🔍 TokenManager: Loading stored token...")

        // Try to load from Keychain first, fallback to UserDefaults
        var token: String?
        var expiry: Date?

        // Try Keychain first
        do {
            token = try KeychainManager.shared.loadAccessToken()
            print("✅ Token loaded from Keychain")
        } catch {
            // Fallback to UserDefaults
            token = UserDefaults.standard.string(forKey: tokenKey)
            print("📱 Token loaded from UserDefaults (fallback)")
        }

        expiry = UserDefaults.standard.object(forKey: expiryKey) as? Date

        // Check if token exists (don't check expiry here to prevent auto-logout)
        if let token = token {
            currentToken = token
            tokenExpiry = expiry
            isAuthenticated = true
            print("✅ Valid token loaded. Expires: \(expiry?.description ?? "Unknown")")
        } else {
            print("⚠️ Token not found")
            clearToken()
        }
    }

    func getToken() -> String? {
        return isTokenValid ? currentToken : nil
    }

    func clearToken() {
        print("🗑️ TokenManager: Clearing token...")

        // Clear from Keychain
        do {
            try KeychainManager.shared.deleteAccessToken()
            print("✅ Token cleared from Keychain")
        } catch {
            print("⚠️ Failed to clear token from Keychain: \(error)")
        }

        // Clear from UserDefaults
        UserDefaults.standard.removeObject(forKey: tokenKey)
        UserDefaults.standard.removeObject(forKey: userKey)
        UserDefaults.standard.removeObject(forKey: expiryKey)
        UserDefaults.standard.removeObject(forKey: emailKey)

        // Update state
        currentToken = nil
        tokenExpiry = nil
        isAuthenticated = false

        print("✅ Token cleared successfully")
    }

    // MARK: - Computed Properties
    var isTokenValid: Bool {
        guard let token = currentToken else {
            return false
        }

        // Just check if token exists and is not empty
        // Don't check expiry here to prevent auto-logout on app start
        return !token.isEmpty
    }

    var authorizationHeader: String? {
        guard let token = currentToken, isTokenValid else {
            return nil
        }
        return "Bearer \(token)"
    }

    var isLoggedIn: Bool {
        return isTokenValid
    }

    // MARK: - User Management
    func saveUser<T: Codable>(_ user: T) {
        if let encoded = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(encoded, forKey: userKey)
        }
    }

    func getUser<T: Codable>(_ type: T.Type) -> T? {
        guard let data = UserDefaults.standard.data(forKey: userKey) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }

    func saveUserEmail(_ email: String) {
        UserDefaults.standard.set(email, forKey: emailKey)
    }

    var userEmail: String? {
        return UserDefaults.standard.string(forKey: emailKey)
    }
}
