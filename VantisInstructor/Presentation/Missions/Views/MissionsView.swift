//
//  MissionsView.swift
//  mobile-app-template
//
//  Created for Mission System Main View
//

import SwiftUI

struct MissionsView: View {
    @StateObject private var viewModel: MissionViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.presentationMode) var presentationMode
    @State private var showFilters = false
    @State private var selectedTab = 0
    @State private var showMissionDetail = false
    @State private var showLeaderboard = false
    @State private var showBadges = false
    
    init(userId: String) {
        _viewModel = StateObject(wrappedValue: MissionViewModel(userId: userId))
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // Modern Header Section
                modernHeaderSection

                // Level and XP Progress Section
                levelProgressSection

                // Tab selector
                missionTabSelector

                // Content based on selected tab
                missionContentView
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
        }
        .navigationBarHidden(true)
        .background(
            Color.white
                .ignoresSafeArea()
        )
            .sheet(isPresented: $showMissionDetail) {
                if let mission = viewModel.selectedMission {
                    MissionDetailView(mission: mission, viewModel: viewModel)
                }
            }
            .sheet(isPresented: $showLeaderboard) {
                MissionLeaderboardView(viewModel: viewModel)
            }
            .sheet(isPresented: $showBadges) {
                BadgeCollectionView(viewModel: viewModel)
            }
            .sheet(isPresented: $showFilters) {
                MissionFiltersView(viewModel: viewModel)
            }
            .overlay(
                Group {
                    if viewModel.showRewardAnimation,
                       let reward = viewModel.lastEarnedReward {
                        RewardAnimationView(reward: reward)
                    }
                }
            )
            .task {
                viewModel.loadMissions()
            }
    }

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Nhiệm vụ")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Hoàn thành nhiệm vụ và nhận thưởng")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            Menu {
                Button {
                    showLeaderboard = true
                } label: {
                    Label("Bảng xếp hạng", systemImage: "chart.bar.fill")
                }

                Button {
                    showBadges = true
                } label: {
                    Label("Huy hiệu", systemImage: "star.circle.fill")
                }

                Button {
                    showFilters = true
                } label: {
                    Label("Lọc", systemImage: "line.3.horizontal.decrease.circle")
                }
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 14, weight: .medium))
                    Text("Tùy chọn")
                        .font(.beVietnamPro(.medium, size: 14))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.bottom, 24)
    }

    // MARK: - Level Progress Section
    private var levelProgressSection: some View {
        VStack(spacing: 16) {
            // Level and XP Progress
            HStack(spacing: 20) {
                // Level Badge
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            colors: [AppConstants.Colors.primary, AppConstants.Colors.primary.opacity(0.7)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 60, height: 60)
                    
                    VStack(spacing: 2) {
                        Text("LV")
                            .font(.beVietnamPro(.bold, size: 12))
                        Text("\(viewModel.currentLevel)")
                            .font(.beVietnamPro(.bold, size: 20))
                    }
                    .foregroundColor(.white)
                }
                
                // XP Progress
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("\(viewModel.totalXP) XP")
                            .font(.beVietnamPro(.semiBold, size: 18))
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Spacer()

                        if let stats = viewModel.userStats {
                            Text("\(stats.nextLevelXP) XP")
                                .font(.beVietnamPro(.medium, size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                    
                    // Progress Bar
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(AppConstants.Colors.cardBackground)
                                .frame(height: 8)
                            
                            RoundedRectangle(cornerRadius: 4)
                                .fill(AppConstants.Colors.primary)
                                .frame(width: geometry.size.width * viewModel.levelProgress, height: 8)
                        }
                    }
                    .frame(height: 8)
                }
            }
            
            // Streak Info
            if !viewModel.streaks.isEmpty {
                HStack(spacing: 16) {
                    ForEach(viewModel.streaks.prefix(2), id: \.type) { streak in
                        StreakBadge(streak: streak)
                    }
                    Spacer()
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        .padding(.bottom, 24)
    }
    
    // MARK: - Tab Selector
    private var missionTabSelector: some View {
        HStack(spacing: 0) {
            ForEach(0..<3) { index in
                Button {
                    withAnimation(.spring()) {
                        selectedTab = index
                    }
                } label: {
                    VStack(spacing: 8) {
                        Text(tabTitle(for: index))
                            .font(.beVietnamPro(selectedTab == index ? .semiBold : .medium, size: 16))
                            .foregroundColor(selectedTab == index ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                        
                        Rectangle()
                            .fill(selectedTab == index ? AppConstants.Colors.primary : Color.clear)
                            .frame(height: 2)
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.bottom, 24)
    }

    // MARK: - Mission Content View
    private var missionContentView: some View {
        Group {
            switch selectedTab {
            case 0:
                activeMissionsContentView
            case 1:
                recurringMissionsContentView
            case 2:
                completedMissionsContentView
            default:
                activeMissionsContentView
            }
        }
    }

    // MARK: - Active Missions Content View
    private var activeMissionsContentView: some View {
        VStack(spacing: 16) {
            if viewModel.isLoading {
                ForEach(0..<3, id: \.self) { _ in
                    MissionCardSkeleton()
                }
            } else if viewModel.filteredMissions.isEmpty {
                EmptyMissionsView()
                    .padding(.top, 40)
            } else {
                ForEach(viewModel.filteredMissions) { mission in
                    MissionCard(mission: mission) {
                        viewModel.selectMission(mission)
                        showMissionDetail = true
                    }
                }
            }

            Spacer(minLength: 80)
        }
    }

    // MARK: - Recurring Missions Content View
    private var recurringMissionsContentView: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Daily Missions
            if !viewModel.dailyMissions.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Nhiệm vụ hàng ngày")
                        .font(.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    ForEach(viewModel.dailyMissions) { mission in
                        MissionCard(mission: mission, compact: true) {
                            viewModel.selectMission(mission)
                            showMissionDetail = true
                        }
                    }
                }
            }

            // Weekly Missions
            if !viewModel.weeklyMissions.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Nhiệm vụ hàng tuần")
                        .font(.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    ForEach(viewModel.weeklyMissions) { mission in
                        MissionCard(mission: mission, compact: true) {
                            viewModel.selectMission(mission)
                            showMissionDetail = true
                        }
                    }
                }
            }

            Spacer(minLength: 80)
        }
    }

    // MARK: - Completed Missions Content View
    private var completedMissionsContentView: some View {
        VStack(spacing: 16) {
            if viewModel.completedMissions.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "checkmark.seal")
                        .font(.system(size: 60))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("Chưa có nhiệm vụ hoàn thành")
                        .font(.headline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.top, 60)
            } else {
                ForEach(viewModel.completedMissions) { mission in
                    CompletedMissionCard(mission: mission) {
                        if mission.status == .completed {
                            viewModel.claimReward(for: mission.id)
                        }
                    }
                }
            }

            Spacer(minLength: 80)
        }
    }




    
    // MARK: - Helper Methods
    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Đang thực hiện"
        case 1: return "Hàng ngày/Tuần"
        case 2: return "Hoàn thành"
        default: return ""
        }
    }
}

// MARK: - Streak Badge Component
struct StreakBadge: View {
    let streak: MissionStreak
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "flame.fill")
                .font(.system(size: 16))
                .foregroundColor(streak.isActive ? .orange : .gray)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("\(streak.currentStreak) \(streak.type == .daily ? "ngày" : "tuần")")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("x\(String(format: "%.1f", streak.streakMultiplier))")
                    .font(.beVietnamPro(.medium, size: 10))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(streak.isActive ? Color.orange.opacity(0.1) : Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

// MARK: - Empty Missions View
struct EmptyMissionsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "target")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text("Không có nhiệm vụ nào")
                .font(.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Các nhiệm vụ mới sẽ xuất hiện hàng ngày")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(40)
    }
}

// MARK: - Mission Card Skeleton
struct MissionCardSkeleton: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 40, height: 40)
                
                VStack(alignment: .leading, spacing: 4) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 150, height: 16)
                    
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 200, height: 12)
                }
                
                Spacer()
            }
            
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 8)
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
        .redacted(reason: .placeholder)
    }
}

// MARK: - Preview
#Preview {
    MissionsView(userId: "user_1")
}
