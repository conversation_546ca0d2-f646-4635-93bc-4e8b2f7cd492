//
//  EditProfileView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct EditProfileView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var instructorService = InstructorProfileService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var fullName = ""
    @State private var username = ""
    @State private var phone = ""
    @State private var email = ""
    @State private var address = ""
    @State private var emergencyContact = ""
    @State private var emergencyPhone = ""
    @State private var bio = ""
    @State private var teachingPhilosophy = ""
    @State private var gender = "male"
    @State private var dateOfBirth = Date()
    @State private var showDatePicker = false
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showSuccess = false
    @FocusState private var focusedField: EditProfileField?

    enum EditProfileField {
        case phone, email, address, emergencyContact, emergencyPhone, bio, teachingPhilosophy
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection

                // Avatar Section
                avatarSection

                // Personal Information
                personalInfoSection

                // Contact Information
                contactInfoSection

                // Professional Information
                professionalInfoSection

                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 20)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .onTapGesture {
            hideKeyboard()
        }
        .task {
            await loadProfileData()
        }
        .alert("Lỗi", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Hồ sơ đã được cập nhật thành công!")
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Chỉnh sửa hồ sơ")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Cập nhật thông tin của bạn")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            Button(action: {
                Task {
                    await saveProfile()
                }
            }) {
                HStack(spacing: 6) {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .medium))
                        Text("Lưu")
                            .font(.beVietnamPro(.medium, size: 14))
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            hasChanges && !isLoading ? AppConstants.Colors.primary : Color.gray,
                            hasChanges && !isLoading ? AppConstants.Colors.primaryDeep : Color.gray.opacity(0.8)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(
                    color: hasChanges && !isLoading ? AppConstants.Colors.primary.opacity(0.3) : Color.clear,
                    radius: 8, x: 0, y: 4
                )
            }
            .disabled(isLoading || !hasChanges)
        }
        .padding(.bottom, 8)
    }

    // MARK: - Avatar Section
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                // TODO: Implement photo picker
            }) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                    
                    if let avatarUrl = authViewModel.currentUser?.avatar {
                        AsyncImage(url: URL(string: avatarUrl)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Text(authViewModel.currentUser?.initials ?? "U")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                    } else {
                        Text(authViewModel.currentUser?.initials ?? "U")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                    
                    // Camera icon
                    Circle()
                        .fill(AppConstants.Colors.surface)
                        .frame(width: 36, height: 36)
                        .overlay(
                            Image(systemName: "camera.fill")
                                .font(.subheadline)
                                .foregroundColor(AppConstants.Colors.primary)
                        )
                        .offset(x: 40, y: 40)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            Text("Nhấn để thay đổi ảnh")
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Personal Information Section
    private var personalInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin cá nhân")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: AppConstants.UI.itemSpacing) {
                // Full Name (Read-only)
                VStack(alignment: .leading, spacing: 8) {
                    Text("Họ và tên")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    HStack {
                        Text(fullName.isEmpty ? "Not provided" : fullName)
                            .foregroundColor(fullName.isEmpty ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)

                        Spacer()

                        Image(systemName: "lock.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .font(.caption)
                    }
                    .padding()
                    .background(AppConstants.Colors.surface)
                    .cornerRadius(AppConstants.UI.cornerRadius)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                            .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                    )
                }

                // Username (Read-only)
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tên đăng nhập")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    HStack {
                        Text(username.isEmpty ? "Not provided" : username)
                            .foregroundColor(username.isEmpty ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)

                        Spacer()

                        Image(systemName: "lock.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .font(.caption)
                    }
                    .padding()
                    .background(AppConstants.Colors.surface)
                    .cornerRadius(AppConstants.UI.cornerRadius)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                            .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                    )
                }

                // Date of Birth
                VStack(alignment: .leading, spacing: 8) {
                    Text("Ngày sinh")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Button(action: {
                        showDatePicker = true
                    }) {
                        HStack {
                            Text(dateOfBirth.formatted(date: .abbreviated, time: .omitted))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Spacer()

                            Image(systemName: "calendar")
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        .padding()
                        .background(Color.white)
                        .cornerRadius(AppConstants.UI.cornerRadius)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                // Gender
                VStack(alignment: .leading, spacing: 8) {
                    Text("Giới tính")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    CustomGenderPicker(selectedGender: $gender)
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $dateOfBirth)
        }
    }
    
    // MARK: - Contact Information Section
    private var contactInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin liên hệ")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: AppConstants.UI.itemSpacing) {
                // Email
                VStack(alignment: .leading, spacing: 8) {
                    Text("Email")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập email của bạn", text: $email)
                        .textFieldStyle()
                        .keyboardType(.emailAddress)
                        .textContentType(.emailAddress)
                        .focused($focusedField, equals: .email)
                        .onSubmit {
                            focusedField = .phone
                        }
                }
                
                // Phone
                VStack(alignment: .leading, spacing: 8) {
                    Text("Số điện thoại")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập số điện thoại của bạn", text: $phone)
                        .textFieldStyle()
                        .keyboardType(.phonePad)
                        .textContentType(.telephoneNumber)
                        .focused($focusedField, equals: .phone)
                        .onSubmit {
                            focusedField = .address
                        }

                    if !phone.isEmpty && !phone.isValidPhone {
                        Text("Vui lòng nhập số điện thoại hợp lệ")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }

                // Address
                VStack(alignment: .leading, spacing: 8) {
                    Text("Địa chỉ")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập địa chỉ của bạn", text: $address)
                        .textFieldStyle()
                        .focused($focusedField, equals: .address)
                        .onSubmit {
                            focusedField = .emergencyContact
                        }
                }

                // Emergency Contact
                VStack(alignment: .leading, spacing: 8) {
                    Text("Liên hệ khẩn cấp")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập tên người liên hệ khẩn cấp", text: $emergencyContact)
                        .textFieldStyle()
                        .focused($focusedField, equals: .emergencyContact)
                        .onSubmit {
                            focusedField = .emergencyPhone
                        }
                }

                // Emergency Phone
                VStack(alignment: .leading, spacing: 8) {
                    Text("Số điện thoại khẩn cấp")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập số điện thoại khẩn cấp", text: $emergencyPhone)
                        .textFieldStyle()
                        .keyboardType(.phonePad)
                        .textContentType(.telephoneNumber)
                        .focused($focusedField, equals: .emergencyPhone)
                        .onSubmit {
                            focusedField = .bio
                        }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }

    // MARK: - Professional Information Section
    private var professionalInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin nghề nghiệp")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: AppConstants.UI.itemSpacing) {
                // Bio
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tiểu sử")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Hãy kể về bản thân bạn", text: $bio, axis: .vertical)
                        .textFieldStyle()
                        .lineLimit(3...6)
                        .focused($focusedField, equals: .bio)
                        .onSubmit {
                            focusedField = .teachingPhilosophy
                        }
                }

                // Teaching Philosophy
                VStack(alignment: .leading, spacing: 8) {
                    Text("Triết lý giảng dạy")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Chia sẻ triết lý giảng dạy của bạn", text: $teachingPhilosophy, axis: .vertical)
                        .textFieldStyle()
                        .lineLimit(3...6)
                        .focused($focusedField, equals: .teachingPhilosophy)
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }

    // MARK: - Computed Properties
    private var hasChanges: Bool {
        // Check if any field has been modified from initial values
        if let profile = instructorService.currentProfile {
            return phone != (profile.contactInfo.phone ?? "") ||
                   email != (profile.contactInfo.email ?? "") ||
                   address != (profile.contactInfo.address ?? "") ||
                   emergencyContact != (profile.contactInfo.emergencyContact ?? "") ||
                   emergencyPhone != (profile.contactInfo.emergencyPhone ?? "") ||
                   gender != (profile.gender ?? "male")
        } else if let user = authViewModel.currentUser {
            return phone != (user.phone ?? "")
        }
        return false
    }
    
    // MARK: - Helper Methods
    private func loadProfileData() async {
        // First load from current user (immediate display)
        loadCurrentUserData()

        // Then load from API (fresh data)
        do {
            let profile = try await instructorService.fetchProfile()

            await MainActor.run {
                fullName = profile.name
                username = profile.userName
                phone = profile.contactInfo.phone ?? ""
                email = profile.contactInfo.email ?? ""
                address = profile.contactInfo.address ?? ""
                emergencyContact = profile.contactInfo.emergencyContact ?? ""
                emergencyPhone = profile.contactInfo.emergencyPhone ?? ""
                gender = profile.gender ?? "male"

                if let dob = profile.birthDate {
                    dateOfBirth = dob
                }
            }
        } catch {
            print("Failed to load profile: \(error)")
            // Keep using current user data if API fails
        }
    }

    private func loadCurrentUserData() {
        guard let user = authViewModel.currentUser else { return }

        fullName = "\(user.firstName ?? "") \(user.lastName ?? "")".trimmingCharacters(in: .whitespaces)
        username = user.email ?? "" // Use email as username fallback
        phone = user.phone ?? ""

        if let dob = user.dateOfBirth {
            dateOfBirth = dob
        }
    }
    
    private func saveProfile() async {
        guard hasChanges else {
            dismiss()
            return
        }

        isLoading = true

        do {
            // Format date for API
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            let dobString = formatter.string(from: dateOfBirth)

            let updateRequest = InstructorProfileUpdate(
                birthDate: dobString,
                gender: gender,
                email: email.isEmpty ? nil : email,
                phone: phone.isEmpty ? nil : phone,
                address: address.isEmpty ? nil : address,
                emergencyContact: emergencyContact.isEmpty ? nil : emergencyContact,
                emergencyPhone: emergencyPhone.isEmpty ? nil : emergencyPhone,
                bio: bio.isEmpty ? nil : bio,
                teachingPhilosophy: teachingPhilosophy.isEmpty ? nil : teachingPhilosophy
            )

            let updatedProfile = try await instructorService.updateProfile(updateRequest)

            // Update the current user in auth state
            await authViewModel.refreshUserData()

            await MainActor.run {
                showSuccess = true
            }

        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
                showError = true
            }
        }

        await MainActor.run {
            isLoading = false
        }
    }
}

// MARK: - Date Picker Sheet
struct DatePickerSheet: View {
    @Binding var selectedDate: Date
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "Select Date",
                    selection: $selectedDate,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(WheelDatePickerStyle())
                .padding()
                
                Spacer()
            }
            .navigationTitle("Date of Birth")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - Custom Gender Picker
struct CustomGenderPicker: View {
    @Binding var selectedGender: String

    private let genderOptions = [
        ("male", "Nam", "person.fill"),
        ("female", "Nữ", "person.fill"),
        ("other", "Khác", "person.2.fill")
    ]

    var body: some View {
        HStack(spacing: 8) {
            ForEach(genderOptions, id: \.0) { option in
                GenderOptionButton(
                    value: option.0,
                    title: option.1,
                    icon: option.2,
                    isSelected: selectedGender == option.0,
                    onTap: {
                        selectedGender = option.0
                    }
                )
            }
        }
    }
}

// MARK: - Gender Option Button
struct GenderOptionButton: View {
    let value: String
    let title: String
    let icon: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.textSecondary)

                Text(title)
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isSelected
                        ? LinearGradient(
                            gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        : LinearGradient(
                            gradient: Gradient(colors: [Color.white, Color.white]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isSelected ? Color.clear : AppConstants.Colors.border,
                        lineWidth: 0.5
                    )
            )
            .shadow(
                color: isSelected ? AppConstants.Colors.primary.opacity(0.3) : Color.clear,
                radius: 4,
                x: 0,
                y: 2
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Preview
#Preview {
    EditProfileView()
        .environmentObject(AuthViewModel())
}
